# -*- coding: utf-8 -*-
"""
YOLOv11 可视化检测界面

功能：
1. 选择YOLOv11模型文件
2. 单张图片检测
3. 文件夹批量检测
4. 视频检测
5. 摄像头检测（本地/IP摄像头）
6. 裂缝检测
"""
# 添加在文件最开头（所有import之前）
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'  # 允许重复加载OpenMP

import sys
import cv2
import time
import numpy as np
import csv
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QFileDialog, QComboBox, QLineEdit, 
                             QMessageBox, QProgressBar, QCheckBox, QGroupBox, QRadioButton,
                             QButtonGroup, QSlider, QSpinBox, QDoubleSpinBox, QTabWidget)
from crack_detection import CrackDetector
from PyQt5.QtGui import QPixmap, QImage, QFont, QIcon
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize

# 确保ultralytics已安装
try:
    from ultralytics import YOLO
except ImportError:
    print("请先安装ultralytics库: pip install ultralytics")
    sys.exit(1)

class CrackDetectionThread(QThread):
    """裂缝检测线程"""
    update_frame = pyqtSignal(np.ndarray, np.ndarray)
    finished = pyqtSignal()
    progress = pyqtSignal(int)
    result_data = pyqtSignal(dict)
    
    def __init__(self, image_path, scale_factor=0.1, unit='mm²'):
        super().__init__()
        self.image_path = image_path
        self.scale_factor = scale_factor
        self.unit = unit
        self.is_running = True
        
    def run(self):
        # 加载图像
        image = cv2.imread(self.image_path)
        if image is not None:
            detector = CrackDetector(scale_factor=self.scale_factor, unit=self.unit)
            contours, result, debug_images = detector.detect_cracks(image, return_debug_images=True)
            pixel_area, real_area, contour_areas = detector.calculate_area(contours)
            
            # 可视化结果
            result_with_info = detector.visualize_results(result, contours, pixel_area, real_area)
            
            # 发送结果
            self.update_frame.emit(image, result_with_info)
            
            # 发送详细数据
            result_data = {
                "contours": contours,
                "contour_count": len(contours),
                "pixel_area": pixel_area,
                "real_area": real_area,
                "contour_areas": contour_areas,
                "debug_images": debug_images
            }
            self.result_data.emit(result_data)
            
        self.finished.emit()

class VideoThread(QThread):
    """视频处理线程"""
    update_frame = pyqtSignal(np.ndarray, np.ndarray)
    finished = pyqtSignal()
    progress = pyqtSignal(int)
    
    def __init__(self, model, source, conf_thres=0.25, save_video=False, save_path=None):
        super().__init__()
        self.model = model
        self.source = source
        self.conf_thres = conf_thres
        self.save_video = save_video
        self.save_path = save_path
        self.is_running = True
        
    def run(self):
        # 打开视频源
        if self.source.isdigit() or self.source.startswith('rtsp://') or self.source.startswith('http://'):
            cap = cv2.VideoCapture(int(self.source) if self.source.isdigit() else self.source)
        else:
            cap = cv2.VideoCapture(self.source)
            
        if not cap.isOpened():
            QMessageBox.critical(None, "错误", f"无法打开视频源: {self.source}")
            self.finished.emit()
            return
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 设置视频保存
        video_writer = None
        if self.save_video and self.save_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(self.save_path, fourcc, fps, (width, height))
        
        frame_count = 0
        while self.is_running:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 使用YOLOv11进行检测
            original_frame = frame.copy()
            results = self.model.predict(frame, conf=self.conf_thres, verbose=False)[0]
            annotated_frame = results.plot()
            
            # 保存视频
            if video_writer is not None:
                video_writer.write(annotated_frame)
            
            # 发送信号更新UI
            self.update_frame.emit(original_frame, annotated_frame)
            
            # 更新进度
            if total_frames > 0:  # 仅对文件视频有效
                progress = int((frame_count / total_frames) * 100)
                self.progress.emit(progress)
                
            frame_count += 1
            
            # 处理按键事件
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # 释放资源
        cap.release()
        if video_writer is not None:
            video_writer.release()
        
        self.finished.emit()
    
    def stop(self):
        self.is_running = False

class FolderProcessThread(QThread):
    """文件夹批处理线程"""
    update_frame = pyqtSignal(np.ndarray, np.ndarray)
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    
    def __init__(self, model, folder_path, save_dir, conf_thres=0.25):
        super().__init__()
        self.model = model
        self.folder_path = folder_path
        self.save_dir = save_dir
        self.conf_thres = conf_thres
        self.is_running = True
        
    def run(self):
        # 获取所有图片文件
        image_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"]
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(Path(self.folder_path).glob(f"*{ext}")))
            image_files.extend(list(Path(self.folder_path).glob(f"*{ext.upper()}")))
        
        if not image_files:
            QMessageBox.warning(None, "警告", "文件夹中没有找到图片文件")
            self.finished.emit()
            return
        
        # 创建保存目录
        os.makedirs(self.save_dir, exist_ok=True)
        
        # 处理每张图片
        total = len(image_files)
        for i, img_path in enumerate(image_files):
            if not self.is_running:
                break
                
            # 读取图片
            img = cv2.imread(str(img_path))
            if img is None:
                continue
                
            # 保存原始图片副本
            original_img = img.copy()
            
            # 使用YOLOv11进行检测
            results = self.model.predict(img, conf=self.conf_thres, verbose=False)[0]
            annotated_img = results.plot()
            
            # 保存结果
            save_path = os.path.join(self.save_dir, img_path.name)
            cv2.imwrite(save_path, annotated_img)
            
            # 更新UI
            self.update_frame.emit(original_img, annotated_img)
            
            # 更新进度
            progress = int(((i + 1) / total) * 100)
            self.progress.emit(progress)
            
            # 短暂延迟，让UI有时间更新
            time.sleep(0.1)
        
        self.finished.emit()
    
    def stop(self):
        self.is_running = False

class YOLOv11GUI(QMainWindow):
    """YOLOv11 GUI主界面"""
    def __init__(self):
        super().__init__()
        self.model = None
        self.video_thread = None
        self.folder_thread = None
        self.camera_id = 0
        self.crack_thread = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置窗口
        self.setWindowTitle("YOLOv11钢筋检测系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主部件和布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        
        # 创建顶部控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        
        # 模型选择区域
        model_group = QGroupBox("模型选择")
        model_layout = QVBoxLayout(model_group)
        
        model_path_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setReadOnly(True)
        self.model_path_edit.setPlaceholderText("选择YOLOv11模型文件(.pt)")
        browse_model_btn = QPushButton("浏览")
        browse_model_btn.clicked.connect(self.browse_model)
        model_path_layout.addWidget(self.model_path_edit)
        model_path_layout.addWidget(browse_model_btn)
        
        load_model_btn = QPushButton("加载模型")
        load_model_btn.clicked.connect(self.load_model)
        
        model_layout.addLayout(model_path_layout)
        model_layout.addWidget(load_model_btn)
        
        # 参数设置区域
        param_group = QGroupBox("参数设置")
        param_layout = QVBoxLayout(param_group)
        
        conf_layout = QHBoxLayout()
        conf_layout.addWidget(QLabel("置信度阈值:"))
        self.conf_slider = QSlider(Qt.Horizontal)
        self.conf_slider.setRange(1, 99)
        self.conf_slider.setValue(25)  # 默认0.25
        self.conf_slider.setTickPosition(QSlider.TicksBelow)
        self.conf_slider.setTickInterval(10)
        self.conf_value_label = QLabel("0.25")
        self.conf_slider.valueChanged.connect(self.update_conf_value)
        conf_layout.addWidget(self.conf_slider)
        conf_layout.addWidget(self.conf_value_label)
        
        save_results_layout = QHBoxLayout()
        self.save_results_cb = QCheckBox("保存检测结果")
        self.save_results_cb.setChecked(True)
        save_results_layout.addWidget(self.save_results_cb)
        
        param_layout.addLayout(conf_layout)
        param_layout.addLayout(save_results_layout)
        
        # 添加到控制面板
        control_layout.addWidget(model_group)
        control_layout.addWidget(param_group)
        
        # 创建图像显示区域
        display_widget = QWidget()
        display_layout = QHBoxLayout(display_widget)
        
        # 原始图像
        original_group = QGroupBox("原始图像")
        original_layout = QVBoxLayout(original_group)
        self.original_label = QLabel()
        self.original_label.setAlignment(Qt.AlignCenter)
        self.original_label.setMinimumSize(500, 400)
        self.original_label.setStyleSheet("background-color: black;")
        original_layout.addWidget(self.original_label)
        
        # 检测结果图像
        result_group = QGroupBox("检测结果")
        result_layout = QVBoxLayout(result_group)
        self.result_label = QLabel()
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumSize(500, 400)
        self.result_label.setStyleSheet("background-color: black;")
        result_layout.addWidget(self.result_label)
        
        display_layout.addWidget(original_group)
        display_layout.addWidget(result_group)
        
        # 创建功能选项卡
        tabs = QTabWidget()
        
        # 单张图片检测选项卡
        image_tab = QWidget()
        image_layout = QVBoxLayout(image_tab)
        
        image_path_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setReadOnly(True)
        self.image_path_edit.setPlaceholderText("选择图片文件")
        browse_image_btn = QPushButton("浏览图片")
        browse_image_btn.clicked.connect(self.browse_image)
        detect_image_btn = QPushButton("检测图片")
        detect_image_btn.clicked.connect(self.detect_image)
        detect_crack_btn = QPushButton("检测裂缝")
        detect_crack_btn.clicked.connect(self.detect_crack)
        image_path_layout.addWidget(self.image_path_edit)
        image_path_layout.addWidget(browse_image_btn)
        image_path_layout.addWidget(detect_image_btn)
        image_path_layout.addWidget(detect_crack_btn)
        
        image_layout.addLayout(image_path_layout)
        
        # 文件夹批量检测选项卡
        folder_tab = QWidget()
        folder_layout = QVBoxLayout(folder_tab)
        
        folder_path_layout = QHBoxLayout()
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setReadOnly(True)
        self.folder_path_edit.setPlaceholderText("选择图片文件夹")
        browse_folder_btn = QPushButton("浏览文件夹")
        browse_folder_btn.clicked.connect(self.browse_folder)
        folder_path_layout.addWidget(self.folder_path_edit)
        folder_path_layout.addWidget(browse_folder_btn)
        
        process_folder_btn = QPushButton("批量处理文件夹")
        process_folder_btn.clicked.connect(self.process_folder)
        
        self.folder_progress = QProgressBar()
        self.folder_progress.setValue(0)
        
        folder_layout.addLayout(folder_path_layout)
        folder_layout.addWidget(process_folder_btn)
        folder_layout.addWidget(self.folder_progress)
        
        # 视频检测选项卡
        video_tab = QWidget()
        video_layout = QVBoxLayout(video_tab)
        
        video_path_layout = QHBoxLayout()
        self.video_path_edit = QLineEdit()
        self.video_path_edit.setReadOnly(True)
        self.video_path_edit.setPlaceholderText("选择视频文件")
        browse_video_btn = QPushButton("浏览视频")
        browse_video_btn.clicked.connect(self.browse_video)
        video_path_layout.addWidget(self.video_path_edit)
        video_path_layout.addWidget(browse_video_btn)
        
        video_control_layout = QHBoxLayout()
        start_video_btn = QPushButton("开始检测")
        start_video_btn.clicked.connect(self.start_video_detection)
        stop_video_btn = QPushButton("停止检测")
        stop_video_btn.clicked.connect(self.stop_detection)
        video_control_layout.addWidget(start_video_btn)
        video_control_layout.addWidget(stop_video_btn)
        
        self.video_progress = QProgressBar()
        self.video_progress.setValue(0)
        
        video_layout.addLayout(video_path_layout)
        video_layout.addLayout(video_control_layout)
        video_layout.addWidget(self.video_progress)
        
        # 摄像头检测选项卡
        camera_tab = QWidget()
        camera_layout = QVBoxLayout(camera_tab)
        
        camera_id_layout = QHBoxLayout()
        camera_id_layout.addWidget(QLabel("摄像头ID:"))
        self.camera_id_edit = QLineEdit("0")
        camera_id_layout.addWidget(self.camera_id_edit)
        
        camera_control_layout = QHBoxLayout()
        start_camera_btn = QPushButton("开始检测")
        start_camera_btn.clicked.connect(self.start_camera_detection)
        stop_camera_btn = QPushButton("停止检测")
        stop_camera_btn.clicked.connect(self.stop_detection)
        camera_control_layout.addWidget(start_camera_btn)
        camera_control_layout.addWidget(stop_camera_btn)
        
        camera_layout.addLayout(camera_id_layout)
        camera_layout.addLayout(camera_control_layout)
        
        # 添加到选项卡
        tabs.addTab(image_tab, "单张图片检测")
        tabs.addTab(folder_tab, "文件夹批量检测")
        tabs.addTab(video_tab, "视频检测")
        tabs.addTab(camera_tab, "摄像头检测")
        
        # 添加到主布局
        main_layout.addWidget(control_panel)
        main_layout.addWidget(display_widget)
        main_layout.addWidget(tabs)
        
        # 设置主部件
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 初始化模型
        self.load_default_model()
    
    def load_default_model(self):
        """加载默认模型"""
        default_model_path = "yolo11n-seg.pt"
        if os.path.exists(default_model_path):
            self.model_path_edit.setText(default_model_path)
            self.load_model()
    
    def browse_model(self):
        """浏览模型文件"""
        model_path, _ = QFileDialog.getOpenFileName(self, "选择YOLOv11模型文件", "", "模型文件 (*.pt)")
        if model_path:
            self.model_path_edit.setText(model_path)
    
    def load_model(self):
        """加载模型"""
        model_path = self.model_path_edit.text()
        if not model_path:
            QMessageBox.warning(self, "警告", "请先选择模型文件")
            return
        
        try:
            self.model = YOLO(model_path)
            QMessageBox.information(self, "成功", "模型加载成功")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"模型加载失败: {str(e)}")
    
    def update_conf_value(self):
        """更新置信度阈值显示"""
        conf_value = self.conf_slider.value() / 100
        self.conf_value_label.setText(f"{conf_value:.2f}")
    
    def browse_image(self):
        """浏览图片文件"""
        image_path, _ = QFileDialog.getOpenFileName(self, "选择图片文件", "", "图片文件 (*.jpg *.jpeg *.png *.bmp)")
        if image_path:
            self.image_path_edit.setText(image_path)
            
            # 显示原始图片
            pixmap = QPixmap(image_path)
            self.original_label.setPixmap(pixmap.scaled(self.original_label.size(), Qt.KeepAspectRatio))
    
    def detect_image(self):
        """检测单张图片"""
        if self.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return
        
        image_path = self.image_path_edit.text()
        if not image_path:
            QMessageBox.warning(self, "警告", "请先选择图片文件")
            return
        
        try:
            # 读取图片
            img = cv2.imread(image_path)
            if img is None:
                QMessageBox.critical(self, "错误", "无法读取图片")
                return
            
            # 使用YOLOv11进行检测
            conf_thres = self.conf_slider.value() / 100
            results = self.model.predict(img, conf=conf_thres, verbose=False)[0]
            annotated_img = results.plot()
            
            # 显示检测结果
            height, width, channel = annotated_img.shape
            bytes_per_line = 3 * width
            q_img = QImage(annotated_img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_img)
            self.result_label.setPixmap(pixmap.scaled(self.result_label.size(), Qt.KeepAspectRatio))
            
            # 保存结果
            if self.save_results_cb.isChecked():
                save_dir = os.path.join(os.path.dirname(image_path), "yolov11_results")
                os.makedirs(save_dir, exist_ok=True)
                
                filename = os.path.basename(image_path)
                base_name, ext = os.path.splitext(filename)
                save_path = os.path.join(save_dir, f"{base_name}_detected{ext}")
                
                cv2.imwrite(save_path, annotated_img)
                QMessageBox.information(self, "成功", f"结果已保存至: {save_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"检测过程中出错: {str(e)}")
    
    def detect_crack(self):
        """检测裂缝"""
        image_path = self.image_path_edit.text()
        if not image_path:
            QMessageBox.warning(self, "警告", "请先选择图片文件")
            return
        
        # 停止之前的线程
        if self.crack_thread is not None:
            self.crack_thread.terminate()
            self.crack_thread.wait()
        
        # 创建并启动新线程
        self.crack_thread = CrackDetectionThread(image_path)
        self.crack_thread.update_frame.connect(self.update_crack_detection)
        self.crack_thread.finished.connect(self.crack_detection_finished)
        self.crack_thread.start()
    
    def update_crack_detection(self, original_img, result_img):
        """更新裂缝检测结果"""
        # 显示原始图像
        height, width, channel = original_img.shape
        bytes_per_line = 3 * width
        q_img = QImage(original_img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        self.original_label.setPixmap(pixmap.scaled(self.original_label.size(), Qt.KeepAspectRatio))
        
        # 显示检测结果
        height, width, channel = result_img.shape
        bytes_per_line = 3 * width
        q_img = QImage(result_img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        self.result_label.setPixmap(pixmap.scaled(self.result_label.size(), Qt.KeepAspectRatio))
    
    def crack_detection_finished(self):
        """裂缝检测完成"""
        QMessageBox.information(self, "完成", "裂缝检测完成")
    
    def browse_folder(self):
        """浏览文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹")
        if folder_path:
            self.folder_path_edit.setText(folder_path)
    
    def process_folder(self):
        """批量处理文件夹"""
        if self.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return
        
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "警告", "请先选择文件夹")
            return
        
        # 停止之前的线程
        if self.folder_thread is not None:
            self.folder_thread.stop()
            self.folder_thread.wait()
        
        # 创建保存目录
        save_dir = os.path.join(folder_path, "yolov11_results")
        
        # 创建并启动新线程
        conf_thres = self.conf_slider.value() / 100
        self.folder_thread = FolderProcessThread(self.model, folder_path, save_dir, conf_thres)
        self.folder_thread.update_frame.connect(self.update_folder_detection)
        self.folder_thread.progress.connect(self.update_folder_progress)
        self.folder_thread.finished.connect(self.folder_detection_finished)
        self.folder_thread.start()
    
    def update_folder_detection(self, original_img, result_img):
        """更新文件夹检测结果"""
        # 显示原始图像
        height, width, channel = original_img.shape
        bytes_per_line = 3 * width
        q_img = QImage(original_img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        self.original_label.setPixmap(pixmap.scaled(self.original_label.size(), Qt.KeepAspectRatio))
        
        # 显示检测结果
        height, width, channel = result_img.shape
        bytes_per_line = 3 * width
        q_img = QImage(result_img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        self.result_label.setPixmap(pixmap.scaled(self.result_label.size(), Qt.KeepAspectRatio))
    
    def update_folder_progress(self, progress):
        """更新文件夹处理进度"""
        self.folder_progress.setValue(progress)
    
    def folder_detection_finished(self):
        """文件夹检测完成"""
        QMessageBox.information(self, "完成", "文件夹处理完成")
    
    def browse_video(self):
        """浏览视频文件"""
        video_path, _ = QFileDialog.getOpenFileName(self, "选择视频文件", "", "视频文件 (*.mp4 *.avi *.mov *.mkv)")
        if video_path:
            self.video_path_edit.setText(video_path)
    
    def start_video_detection(self):
        """开始视频检测"""
        if self.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return
        
        video_path = self.video_path_edit.text()
        if not video_path:
            QMessageBox.warning(self, "警告", "请先选择视频文件")
            return
        
        # 停止之前的线程
        if self.video_thread is not None:
            self.video_thread.stop()
            self.video_thread.wait()
        
        # 创建保存路径
        save_path = None
        if self.save_results_cb.isChecked():
            save_dir = os.path.join(os.path.dirname(video_path), "yolov11_results")
            os.makedirs(save_dir, exist_ok=True)
            
            filename = os.path.basename(video_path)
            base_name, ext = os.path.splitext(filename)
            save_path = os.path.join(save_dir, f"{base_name}_detected{ext}")
        
        # 创建并启动新线程
        conf_thres = self.conf_slider.value() / 100
        self.video_thread = VideoThread(self.model, video_path, conf_thres, self.save_results_cb.isChecked(), save_path)
        self.video_thread.update_frame.connect(self.update_video_detection)
        self.video_thread.progress.connect(self.update_video_progress)
        self.video_thread.finished.connect(self.video_detection_finished)
        self.video_thread.start()
    
    def update_video_detection(self, original_frame, annotated_frame):
        """更新视频检测结果"""
        # 显示原始帧
        height, width, channel = original_frame.shape
        bytes_per_line = 3 * width
        q_img = QImage(original_frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        self.original_label.setPixmap(pixmap.scaled(self.original_label.size(), Qt.KeepAspectRatio))
        
        # 显示检测结果
        height, width, channel = annotated_frame.shape
        bytes_per_line = 3 * width
        q_img = QImage(annotated_frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)
        self.result_label.setPixmap(pixmap.scaled(self.result_label.size(), Qt.KeepAspectRatio))
    
    def update_video_progress(self, progress):
        """更新视频处理进度"""
        self.video_progress.setValue(progress)
    
    def video_detection_finished(self):
        """视频检测完成"""
        QMessageBox.information(self, "完成", "视频检测完成")
    
    def start_camera_detection(self):
        """开始摄像头检测"""
        if self.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return
        
        camera_id = self.camera_id_edit.text()
        if not camera_id:
            QMessageBox.warning(self, "警告", "请输入摄像头ID")
            return
        
        # 停止之前的线程
        if self.video_thread is not None:
            self.video_thread.stop()
            self.video_thread.wait()
        
        # 创建并启动新线程
        conf_thres = self.conf_slider.value() / 100
        self.video_thread = VideoThread(self.model, camera_id, conf_thres, False, None)
        self.video_thread.update_frame.connect(self.update_video_detection)
        self.video_thread.finished.connect(self.video_detection_finished)
        self.video_thread.start()
    
    def stop_detection(self):
        """停止检测"""
        if self.video_thread is not None:
            self.video_thread.stop()
            self.video_thread.wait()
        
        if self.folder_thread is not None:
            self.folder_thread.stop()
            self.folder_thread.wait()
        
        if self.crack_thread is not None:
            self.crack_thread.terminate()
            self.crack_thread.wait()
    
    def closeEvent(self, event):
        """关闭窗口事件"""
        self.stop_detection()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 创建主窗口
    window = YOLOv11GUI()
    window.show()
    
    sys.exit(app.exec_())