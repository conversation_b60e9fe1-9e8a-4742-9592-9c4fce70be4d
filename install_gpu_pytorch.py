# -*- coding: utf-8 -*-
"""
GPU PyTorch安装脚本
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并显示进度"""
    print(f"\n🔄 {description}...")
    print(f"命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=False, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def main():
    """主安装流程"""
    print("=== GPU PyTorch 安装脚本 ===")
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 安装步骤
    steps = [
        {
            'cmd': f'"{sys.executable}" -m pip install --upgrade pip',
            'desc': '升级pip'
        },
        {
            'cmd': f'"{sys.executable}" -m pip uninstall torch torchvision torchaudio -y',
            'desc': '卸载旧版本PyTorch'
        },
        {
            'cmd': f'"{sys.executable}" -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121',
            'desc': '安装GPU版本PyTorch'
        },
        {
            'cmd': f'"{sys.executable}" -m pip install ultralytics',
            'desc': '安装/更新Ultralytics'
        }
    ]
    
    # 执行安装步骤
    for step in steps:
        success = run_command(step['cmd'], step['desc'])
        if not success:
            print(f"\n❌ 安装失败在步骤: {step['desc']}")
            return False
    
    # 验证安装
    print("\n🔍 验证安装...")
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
            print(f"✅ CUDA版本: {torch.version.cuda}")
            
            # 测试GPU
            x = torch.randn(100, 100).cuda()
            y = torch.randn(100, 100).cuda()
            z = torch.mm(x, y)
            print("✅ GPU计算测试成功")
        else:
            print("⚠️  CUDA不可用")
        
        from ultralytics import YOLO
        print("✅ Ultralytics导入成功")
        
        print("\n🎉 安装完成！现在可以运行GPU训练了")
        print("运行: python train.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == '__main__':
    main()
