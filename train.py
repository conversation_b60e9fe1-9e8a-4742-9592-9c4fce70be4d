# -*- coding: utf-8 -*-
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')
import torch
from ultralytics import YOLO

def get_device():
    """自动检测可用设备"""
    if torch.cuda.is_available():
        device = '0'  # 使用第一个GPU
        print(f"检测到CUDA GPU，使用设备: {device}")
    else:
        device = 'cpu'
        print("未检测到CUDA GPU，使用CPU训练")
    return device

if __name__ == '__main__':
    # 自动检测设备
    device = get_device()

    # 根据设备调整批次大小
    if device == 'cpu':
        batch_size = 2  # CPU训练使用较小的批次
        workers = 0     # CPU训练减少工作进程
        print("CPU训练模式：使用较小的批次大小以避免内存不足")
    else:
        batch_size = 4  # GPU训练可以使用更大的批次
        workers = 4     # GPU训练可以使用更多工作进程
        print("GPU训练模式：使用标准批次大小")

    # model.load('yolo11n.pt') # 加载预训练权重,改进或者做对比实验时候不建议打开，因为用预训练模型整体精度没有很明显的提升
    model = YOLO(model=r'E:\yolo\ultralytics-main\ultralytics-main-max-area\yolo11n-seg.pt')

    print(f"开始训练...")
    print(f"设备: {device}")
    print(f"批次大小: {batch_size}")
    print(f"工作进程: {workers}")

    model.train(data=r'E:\yolo\ultralytics-main\ultralytics-main-max-area\data.yaml',
                imgsz=640,
                epochs=300,
                batch=batch_size,
                workers=workers,
                device=device,
                optimizer='SGD',
                close_mosaic=10,
                resume=False,
                project='runs/train',
                name='exp',
                single_cls=False,
                cache=False,
                )
