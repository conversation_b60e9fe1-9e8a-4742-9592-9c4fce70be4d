# 🤖 分割模型选择指南

## 📋 **功能概述**

增强版裂缝检测系统现在支持多种YOLO分割模型选择，让您可以根据具体需求选择最适合的模型。

## 🎯 **支持的模型**

### **YOLO11 系列 (最新推荐)**
| 模型ID | 名称 | 大小 | 速度 | 精度 | 推荐用途 |
|--------|------|------|------|------|----------|
| `yolo11n-seg` | YOLO11 Nano | 6.7MB | 1.5ms | 中等 | 实时检测、资源受限 |
| `yolo11s-seg` | YOLO11 Small | 21.5MB | 2.3ms | 良好 | 通用检测、平衡性能 |
| `yolo11m-seg` | YOLO11 Medium | 49.7MB | 4.2ms | 高 | 高精度检测、专业应用 |
| `yolo11l-seg` | YOLO11 Large | 86.9MB | 6.8ms | 很高 | 超高精度、研究应用 |
| `yolo11x-seg` | YOLO11 Extra | 140.4MB | 11.3ms | 极高 | 极致精度、科研级 |

### **YOLOv8 系列 (稳定版本)**
| 模型ID | 名称 | 大小 | 速度 | 精度 | 推荐用途 |
|--------|------|------|------|------|----------|
| `yolov8n-seg` | YOLOv8 Nano | 6.7MB | 1.8ms | 中等 | 稳定版本、兼容性优先 |
| `yolov8s-seg` | YOLOv8 Small | 21.5MB | 2.8ms | 良好 | 生产环境、可靠性优先 |
| `yolov8m-seg` | YOLOv8 Medium | 49.7MB | 5.1ms | 高 | 高精度需求、成熟应用 |

### **自定义模型**
| 模型ID | 名称 | 描述 |
|--------|------|------|
| `custom-crack-seg` | 裂缝专用模型 | 专门训练的裂缝分割模型 |

## 🚀 **使用方法**

### **1. 命令行工具**

#### **查看可用模型**
```bash
python enhanced_crack_detect.py --list-models
```

#### **下载模型**
```bash
python enhanced_crack_detect.py --download-model yolo11s-seg
```

#### **使用指定模型检测**
```bash
# 使用模型ID
python enhanced_crack_detect.py --source image.jpg --model-id yolo11s-seg --scale 0.1

# 使用模型文件路径
python enhanced_crack_detect.py --source image.jpg --yolo path/to/model.pt --scale 0.1

# 自动选择模型
python enhanced_crack_detect.py --source image.jpg --scale 0.1
```

### **2. 图形界面**

1. 启动GUI：`python enhanced_crack_gui.py`
2. 在"检测选项"中选择分割模型
3. 点击"刷新"更新模型列表
4. 点击"下载"获取新模型
5. 选择模型后开始检测

### **3. 快速启动脚本**

```bash
python quick_start.py
# 选择 "7. 🤖 分割模型管理"
```

模型管理选项：
- 📋 查看所有可用模型
- ✅ 查看已安装模型  
- ⬇️ 下载模型
- 🗑️ 删除模型
- 💡 获取模型推荐

### **4. Python API**

```python
from crack_detection import EnhancedCrackDetector

# 使用模型ID创建检测器
detector = EnhancedCrackDetector(
    scale_factor=0.1,
    unit='mm²',
    yolo_model_id='yolo11s-seg'  # 指定模型ID
)

# 使用模型路径创建检测器
detector = EnhancedCrackDetector(
    scale_factor=0.1,
    unit='mm²',
    yolo_model_path='path/to/model.pt'  # 指定模型路径
)

# 自动选择模型（推荐）
detector = EnhancedCrackDetector(
    scale_factor=0.1,
    unit='mm²'
    # 系统会自动选择最佳模型
)

# 获取可用模型
available_models = detector.get_available_models()
installed_models = detector.get_installed_models()

# 切换模型
detector.switch_model(model_id='yolo11m-seg')

# 下载模型
detector.download_model('yolo11l-seg')

# 获取当前模型信息
model_info = detector.get_current_model_info()
print(f"当前模型: {model_info['name']}")
```

## 🎯 **模型选择建议**

### **根据使用场景选择**

#### **🚀 实时检测 / 资源受限**
- **推荐**: `yolo11n-seg` 或 `yolov8n-seg`
- **特点**: 最小模型，最快速度
- **适用**: 移动设备、嵌入式系统、实时应用

#### **⚖️ 平衡性能 (推荐大多数用户)**
- **推荐**: `yolo11s-seg`
- **特点**: 速度和精度的最佳平衡
- **适用**: 通用裂缝检测、日常使用

#### **🎯 高精度检测**
- **推荐**: `yolo11m-seg` 或 `yolo11l-seg`
- **特点**: 高精度，适中速度
- **适用**: 专业检测、质量控制、重要项目

#### **🔬 科研级精度**
- **推荐**: `yolo11x-seg`
- **特点**: 极致精度，较慢速度
- **适用**: 科研项目、最高精度要求

#### **🏭 生产环境**
- **推荐**: `yolov8s-seg` 或 `yolov8m-seg`
- **特点**: 稳定可靠，成熟版本
- **适用**: 生产系统、长期部署

### **自动推荐系统**

系统提供智能推荐：
```python
from model_manager import model_manager

# 获取推荐
speed_model = model_manager.recommend_model('speed')      # 速度优先
accuracy_model = model_manager.recommend_model('accuracy') # 精度优先  
balanced_model = model_manager.recommend_model('balanced') # 平衡选择
crack_model = model_manager.recommend_model('crack_specific') # 裂缝专用
```

## 🔧 **高级功能**

### **模型管理**

```python
from model_manager import model_manager

# 查看模型详细信息
model_info = model_manager.get_model_info('yolo11s-seg')
print(f"模型: {model_info['name']}")
print(f"大小: {model_info['size_mb']} MB")
print(f"精度: {model_info['accuracy']}")

# 检查模型是否已安装
is_installed = model_manager.is_model_installed('yolo11s-seg')

# 获取模型文件路径
model_path = model_manager.get_model_path('yolo11s-seg')

# 删除模型
model_manager.remove_model('yolo11s-seg')
```

### **添加自定义模型**

```python
# 添加自定义训练的模型
model_manager.add_custom_model(
    model_id='my-crack-model',
    model_path='/path/to/my_model.pt',
    name='我的裂缝模型',
    description='专门针对特定场景训练的裂缝检测模型'
)
```

## 📊 **性能对比**

| 场景 | 推荐模型 | 检测时间 | 精度 | 内存占用 |
|------|----------|----------|------|----------|
| 实时检测 | yolo11n-seg | ~1.5s | ⭐⭐⭐ | 最低 |
| 通用检测 | yolo11s-seg | ~2.3s | ⭐⭐⭐⭐ | 低 |
| 专业检测 | yolo11m-seg | ~4.2s | ⭐⭐⭐⭐⭐ | 中等 |
| 科研应用 | yolo11l-seg | ~6.8s | ⭐⭐⭐⭐⭐⭐ | 高 |

## 🛠️ **故障排除**

### **常见问题**

#### **Q: 模型下载失败**
A: 检查网络连接，或手动下载模型文件到 `models/` 目录

#### **Q: 模型加载失败**  
A: 确保模型文件完整，尝试重新下载

#### **Q: 检测精度不满意**
A: 尝试更大的模型（如从 nano 升级到 small 或 medium）

#### **Q: 检测速度太慢**
A: 尝试更小的模型（如从 large 降级到 small 或 nano）

### **调试命令**

```bash
# 查看详细信息
python enhanced_crack_detect.py --source image.jpg --model-id yolo11s-seg --verbose

# 查看调试图像
python enhanced_crack_detect.py --source image.jpg --model-id yolo11s-seg --debug
```

## 🎉 **总结**

通过模型选择功能，您可以：

1. **🎯 精确控制**: 根据需求选择最适合的模型
2. **⚡ 优化性能**: 在速度和精度之间找到最佳平衡
3. **🔄 灵活切换**: 随时更换不同的检测模型
4. **📈 持续改进**: 使用最新的YOLO模型技术
5. **🛠️ 自定义扩展**: 添加专门训练的模型

**开始使用**: `python quick_start.py` → 选择 "7. 🤖 分割模型管理"
