@echo off
echo === 创建干净的GPU训练环境 ===

echo 1. 删除旧环境...
conda remove -n yolo-gpu --all -y

echo 2. 创建新环境...
conda create -n yolo-gpu python=3.10 -y

echo 3. 激活环境...
call conda activate yolo-gpu

echo 4. 安装GPU版本PyTorch...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

echo 5. 安装ultralytics...
pip install ultralytics

echo 6. 验证安装...
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

echo 7. 测试YOLO...
python -c "from ultralytics import YOLO; print('Ultralytics OK')"

echo === 环境配置完成 ===
echo 使用方法:
echo conda activate yolo-gpu
echo python train_gpu_simple.py

pause
