# -*- coding: utf-8 -*-
"""
最终GPU训练脚本 - RTX A5000优化版
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')

import torch
from ultralytics import YOL<PERSON>

def main():
    """主训练函数"""
    print("=== RTX A5000 GPU训练开始 ===")
    
    # 检查GPU状态
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU: {gpu_name} ({memory_gb:.1f} GB)")
        
        # RTX A5000 24GB 优化配置
        config = {
            'device': '0',
            'batch': 16,        # 大显存支持大批次
            'workers': 8,       # 多线程数据加载
            'cache': True,      # 启用缓存
            'amp': True,        # 混合精度训练
            'optimizer': 'AdamW'
        }
        print("🚀 使用RTX A5000优化配置")
    else:
        print("❌ CUDA不可用")
        return
    
    # 加载模型
    model_path = r'E:\yolo\ultralytics-main\ultralytics-main-max-area\yolo11n-seg.pt'
    if os.path.exists(model_path):
        model = YOLO(model_path)
        print(f"✅ 加载本地模型: {model_path}")
    else:
        model = YOLO('yolo11n-seg.pt')
        print("✅ 下载并加载模型: yolo11n-seg.pt")
    
    # 训练参数
    train_args = {
        'data': r'E:\yolo\ultralytics-main\ultralytics-main-max-area\data.yaml',
        'imgsz': 640,
        'epochs': 300,
        'batch': config['batch'],
        'workers': config['workers'],
        'device': config['device'],
        'optimizer': config['optimizer'],
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'close_mosaic': 10,
        'resume': False,
        'project': 'runs/train',
        'name': 'crack_detection_rtx_a5000',
        'single_cls': False,
        'cache': config['cache'],
        'amp': config['amp'],
        'save_period': 10,
        'patience': 50,
        'verbose': True,
        'plots': True,
        'val': True,
    }
    
    print(f"\n🚀 开始GPU训练...")
    print(f"设备: {config['device']}")
    print(f"批次大小: {config['batch']}")
    print(f"工作进程: {config['workers']}")
    print(f"混合精度: {config['amp']}")
    print(f"优化器: {config['optimizer']}")
    print(f"训练轮数: {train_args['epochs']}")
    
    try:
        # 清理GPU缓存
        torch.cuda.empty_cache()
        
        # 开始训练
        print("\n" + "="*50)
        print("开始训练...")
        print("="*50)
        
        results = model.train(**train_args)
        
        print("\n" + "="*50)
        print("✅ 训练完成!")
        print(f"最佳模型保存在: {results.save_dir}")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理GPU缓存
        torch.cuda.empty_cache()
        print("\n🧹 GPU缓存已清理")

if __name__ == '__main__':
    main()
