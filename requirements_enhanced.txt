# 增强版裂缝检测系统依赖包
# Enhanced Crack Detection System Requirements

# 核心图像处理库
opencv-python>=4.5.0
numpy>=1.19.0
matplotlib>=3.3.0

# 科学计算和机器学习
scikit-image>=0.18.0
scikit-learn>=0.24.0
scipy>=1.7.0

# 深度学习框架
ultralytics>=8.0.0
torch>=1.9.0
torchvision>=0.10.0

# GUI界面
PyQt5>=5.15.0

# 数据处理
pandas>=1.3.0
pathlib2>=2.3.0

# 系统和工具
datetime
csv
os
sys
argparse
time
threading

# 可选依赖 (用于特定功能)
# Jupyter notebook支持
# jupyter>=1.0.0
# ipython>=7.0.0

# 图像增强处理
# pillow>=8.0.0
# imageio>=2.9.0

# 数据可视化增强
# seaborn>=0.11.0
# plotly>=5.0.0

# 性能优化
# numba>=0.54.0
# cython>=0.29.0

# 测试框架
unittest2>=1.1.0
pytest>=6.0.0

# 文档生成
# sphinx>=4.0.0
# sphinx-rtd-theme>=0.5.0
