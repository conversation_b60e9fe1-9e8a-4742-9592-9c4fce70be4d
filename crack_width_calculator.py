# -*- coding: utf-8 -*-
"""
裂缝宽度计算模块 - 基于正交骨架线法

实现基于图像的裂缝分割与裂缝宽度计算
参考: https://blog.csdn.net/Subtlechange/article/details/118523710

功能：
1. 裂缝骨架线提取（中轴变换）
2. 边缘线检测和提取
3. 法向量估计（SVD方法）
4. 正交骨架线法宽度计算
5. 宽度统计和可视化
"""

import numpy as np
import cv2
from skimage.morphology import skeletonize
from skimage import measure
from sklearn.neighbors import KDTree
import matplotlib.pyplot as plt
from datetime import datetime
import os


class CrackWidthCalculator:
    def __init__(self, scale_factor=1.0, unit='mm'):
        """初始化裂缝宽度计算器
        :param scale_factor: 比例尺（像素/实际单位）
        :param unit: 长度单位，默认为毫米
        """
        self.scale_factor = scale_factor
        self.unit = unit
        
        # 宽度计算参数
        self.width_params = {
            'hband': 2.0,          # 水平带宽
            'vband': 2.0,          # 垂直带宽
            'est_width': 0,        # 预估宽度（0表示自动）
            'knn_neighbors': 5     # KNN邻居数量
        }

    def SVD(self, points):
        """奇异值分解计算主方向
        :param points: 点集
        :return: u, s, c, normal
        """
        pts = points.copy()
        # 奇异值分解
        c = np.mean(pts, axis=0)
        A = pts - c  # shift the points
        A = A.T  # 3*n
        u, s, vh = np.linalg.svd(A, full_matrices=False, compute_uv=True)  # A=u*s*vh
        normal = u[:, -1]

        # 法向量归一化
        nlen = np.sqrt(np.dot(normal, normal))
        normal = normal / nlen
        return u, s, c, normal

    def estimate_normals(self, points, n):
        """估计骨架线上每个点的法向量
        :param points: 骨架线点集
        :param n: 用于估计的邻居点数量
        :return: 法向量数组
        """
        pts = np.copy(points)
        tree = KDTree(pts, leaf_size=2)
        idx = tree.query(pts, k=n, return_distance=False, dualtree=False, breadth_first=False)
        
        normals = []
        for i in range(0, pts.shape[0]):
            pts_for_normals = pts[idx[i, :], :]
            _, _, _, normal = self.SVD(pts_for_normals)
            normals.append(normal)
        normals = np.array(normals)
        return normals

    def estimate_normal_for_pos(self, pos, points, n):
        """估计指定位置的法向量
        :param pos: 需要计算法向量的位置
        :param points: 骨架线点集
        :param n: 用于估计的邻居点数量
        :return: 法向量数组
        """
        pts = np.copy(points)
        tree = KDTree(pts, leaf_size=2)
        idx = tree.query(pos, k=n, return_distance=False, dualtree=False, breadth_first=False)
        
        normals = []
        for i in range(0, pos.shape[0]):
            pts_for_normals = pts[idx[i, :], :]
            _, _, _, normal = self.SVD(pts_for_normals)
            normals.append(normal)
        normals = np.array(normals)
        return normals

    def extract_skeleton(self, binary_mask):
        """提取裂缝骨架线
        :param binary_mask: 二值化裂缝掩码
        :return: 骨架线点集
        """
        # 使用skimage的骨架化算法
        skeleton = skeletonize(binary_mask > 0)
        
        # 提取骨架线坐标
        y, x = np.where(skeleton > 0)
        centers = np.hstack((y.reshape(-1, 1), x.reshape(-1, 1)))
        
        return centers, skeleton

    def extract_contours(self, binary_mask):
        """提取裂缝边缘轮廓
        :param binary_mask: 二值化裂缝掩码
        :return: 边缘点集
        """
        # 使用skimage的轮廓检测
        contours = measure.find_contours(binary_mask, 0.5)
        
        # 合并所有轮廓点
        all_points = []
        for contour in contours:
            all_points.extend(contour)
        
        if all_points:
            bpoints = np.array(all_points)
        else:
            bpoints = np.array([]).reshape(0, 2)
            
        return bpoints

    def get_crack_width_points(self, centers, normals, bpoints, hband=2, vband=2, est_width=0):
        """计算裂缝宽度控制点
        主要算法：正交骨架线法
        :param centers: 骨架线中心点
        :param normals: 法向量
        :param bpoints: 边缘点
        :param hband: 水平带宽
        :param vband: 垂直带宽
        :param est_width: 预估宽度
        :return: 宽度测量线段和宽度值
        """
        cpoints = np.copy(centers)
        cnormals = np.copy(normals)

        xmatrix = np.array([[0, 1], [-1, 0]])
        cnormalsx = np.dot(xmatrix, cnormals.T).T  # x轴法向量
        N = cpoints.shape[0]

        width_segments = []
        widths = []
        
        for i in range(N):
            try:
                ny = cnormals[i]
                nx = cnormalsx[i]
                tform = np.array([nx, ny])
                bpoints_loc = np.dot(tform, bpoints.T).T
                cpoints_loc = np.dot(tform, cpoints.T).T
                ci = cpoints_loc[i]

                # 筛选左右两侧的点
                bl_ind = (bpoints_loc[:, 0] - (ci[0] - hband)) * (bpoints_loc[:, 0] - ci[0]) < 0
                br_ind = (bpoints_loc[:, 0] - ci[0]) * (bpoints_loc[:, 0] - (ci[0] + hband)) <= 0
                bl = bpoints_loc[bl_ind]  # 左侧点
                br = bpoints_loc[br_ind]  # 右侧点

                if len(bl) == 0 or len(br) == 0:
                    continue

                if est_width > 0:
                    # 使用预估宽度筛选
                    half_est_width = est_width / 2
                    blt = bl[(bl[:, 1] - (ci[1] + half_est_width)) * (bl[:, 1] - ci[1]) < 0]
                    blb = bl[(bl[:, 1] - (ci[1] - half_est_width)) * (bl[:, 1] - ci[1]) < 0]
                    brt = br[(br[:, 1] - (ci[1] + half_est_width)) * (br[:, 1] - ci[1]) < 0]
                    brb = br[(br[:, 1] - (ci[1] - half_est_width)) * (br[:, 1] - ci[1]) < 0]
                else:
                    # 使用均值分割
                    blt = bl[bl[:, 1] > np.mean(bl[:, 1])]
                    if np.ptp(blt[:, 1]) > vband:
                        blt = blt[blt[:, 1] > np.mean(blt[:, 1])]

                    blb = bl[bl[:, 1] < np.mean(bl[:, 1])]
                    if np.ptp(blb[:, 1]) > vband:
                        blb = blb[blb[:, 1] < np.mean(blb[:, 1])]

                    brt = br[br[:, 1] > np.mean(br[:, 1])]
                    if np.ptp(brt[:, 1]) > vband:
                        brt = brt[brt[:, 1] > np.mean(brt[:, 1])]

                    brb = br[br[:, 1] < np.mean(br[:, 1])]
                    if np.ptp(brb[:, 1]) > vband:
                        brb = brb[brb[:, 1] < np.mean(brb[:, 1])]

                # 检查是否有足够的点
                if len(blt) == 0 or len(blb) == 0 or len(brt) == 0 or len(brb) == 0:
                    continue

                # 找到最近的边缘点
                t1 = blt[np.argsort(blt[:, 0])[-1]]
                t2 = brt[np.argsort(brt[:, 0])[0]]
                b1 = blb[np.argsort(blb[:, 0])[-1]]
                b2 = brb[np.argsort(brb[:, 0])[0]]

                # 计算交点
                interp1 = (ci[0] - t1[0]) * ((t2[1] - t1[1]) / (t2[0] - t1[0])) + t1[1]
                interp2 = (ci[0] - b1[0]) * ((b2[1] - b1[1]) / (b2[0] - b1[0])) + b1[1]

                if interp1 - ci[1] > 0 and interp2 - ci[1] < 0:
                    width_pixel = abs(interp1 - interp2)
                    width_real = width_pixel * self.scale_factor
                    widths.append([i, width_pixel, width_real])

                    # 转换回原坐标系
                    interps = np.array([[ci[0], interp1], [ci[0], interp2]])
                    interps_rec = np.dot(np.linalg.inv(tform), interps.T).T
                    width_segments.append(interps_rec.reshape(1, -1)[0, :])

            except Exception as e:
                print(f"第 {i} 个点计算失败: {e}")
                continue

        width_segments = np.array(width_segments) if width_segments else np.array([]).reshape(0, 4)
        widths = np.array(widths) if widths else np.array([]).reshape(0, 3)
        
        return width_segments, widths

    def calculate_crack_width(self, binary_mask, return_debug=False):
        """计算裂缝宽度的完整流程
        :param binary_mask: 二值化裂缝掩码
        :param return_debug: 是否返回调试信息
        :return: 宽度计算结果
        """
        try:
            # 1. 提取骨架线
            centers, skeleton = self.extract_skeleton(binary_mask)
            
            if len(centers) == 0:
                return self._empty_result()
            
            # 2. 估计法向量
            normals = self.estimate_normals(centers, self.width_params['knn_neighbors'])
            
            # 3. 提取边缘
            bpoints = self.extract_contours(binary_mask)
            
            if len(bpoints) == 0:
                return self._empty_result()
            
            # 4. 计算宽度
            width_segments, widths = self.get_crack_width_points(
                centers, normals, bpoints,
                hband=self.width_params['hband'],
                vband=self.width_params['vband'],
                est_width=self.width_params['est_width']
            )
            
            # 5. 统计结果
            result = self._process_width_results(width_segments, widths, centers, skeleton, bpoints)
            
            if return_debug:
                result['debug_info'] = {
                    'centers': centers,
                    'normals': normals,
                    'skeleton': skeleton,
                    'bpoints': bpoints,
                    'width_segments': width_segments
                }
            
            return result
            
        except Exception as e:
            print(f"宽度计算失败: {e}")
            return self._empty_result()

    def _empty_result(self):
        """返回空结果"""
        return {
            'width_count': 0,
            'width_measurements': [],
            'statistics': {
                'mean_width': 0,
                'max_width': 0,
                'min_width': 0,
                'std_width': 0
            },
            'total_length': 0,
            'scale_factor': self.scale_factor,
            'unit': self.unit
        }

    def _process_width_results(self, width_segments, widths, centers, skeleton, bpoints):
        """处理宽度计算结果"""
        if len(widths) == 0:
            return self._empty_result()
        
        # 提取宽度值（实际单位）
        width_values = widths[:, 2]  # 第3列是实际宽度
        
        # 计算统计信息
        statistics = {
            'mean_width': float(np.mean(width_values)),
            'max_width': float(np.max(width_values)),
            'min_width': float(np.min(width_values)),
            'std_width': float(np.std(width_values))
        }
        
        # 计算骨架线总长度（像素）
        total_length_pixel = len(centers)
        total_length_real = total_length_pixel * self.scale_factor
        
        return {
            'width_count': len(widths),
            'width_measurements': widths.tolist(),
            'width_segments': width_segments.tolist() if len(width_segments) > 0 else [],
            'statistics': statistics,
            'total_length_pixel': total_length_pixel,
            'total_length_real': total_length_real,
            'scale_factor': self.scale_factor,
            'unit': self.unit
        }

    def set_width_params(self, **kwargs):
        """设置宽度计算参数"""
        for key, value in kwargs.items():
            if key in self.width_params:
                self.width_params[key] = value
                print(f"宽度参数 {key} 已更新为: {value}")
            else:
                print(f"警告: 未知的宽度参数 {key}")

    def visualize_width_results(self, image, result, save_path=None):
        """可视化宽度计算结果"""
        if result['width_count'] == 0:
            print("没有宽度测量结果可显示")
            return image.copy()
        
        vis_image = image.copy()
        
        # 绘制宽度测量线段
        width_segments = np.array(result['width_segments'])
        for segment in width_segments:
            pt1 = (int(segment[1]), int(segment[0]))  # 注意坐标转换
            pt2 = (int(segment[3]), int(segment[2]))
            cv2.line(vis_image, pt1, pt2, (0, 255, 255), 2)  # 黄色线段
        
        # 添加统计信息
        stats = result['statistics']
        y_offset = 30
        font = cv2.FONT_HERSHEY_SIMPLEX
        
        cv2.putText(vis_image, f"宽度测量点数: {result['width_count']}", 
                   (10, y_offset), font, 0.7, (0, 0, 255), 2)
        y_offset += 30
        
        cv2.putText(vis_image, f"平均宽度: {stats['mean_width']:.3f}{self.unit}", 
                   (10, y_offset), font, 0.7, (0, 0, 255), 2)
        y_offset += 30
        
        cv2.putText(vis_image, f"最大宽度: {stats['max_width']:.3f}{self.unit}", 
                   (10, y_offset), font, 0.7, (0, 0, 255), 2)
        y_offset += 30
        
        cv2.putText(vis_image, f"最小宽度: {stats['min_width']:.3f}{self.unit}", 
                   (10, y_offset), font, 0.7, (0, 0, 255), 2)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
            cv2.imwrite(save_path, vis_image)
        
        return vis_image


# 示例用法
if __name__ == "__main__":
    print("=== 裂缝宽度计算模块测试 ===")
    
    # 创建宽度计算器
    width_calculator = CrackWidthCalculator(scale_factor=0.1, unit='mm')
    
    # 测试图像路径
    test_image_path = "test_images/test_crack.jpg"
    
    if os.path.exists(test_image_path):
        # 读取图像
        image = cv2.imread(test_image_path)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 简单二值化（实际应用中应使用更复杂的分割方法）
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
        
        # 计算宽度
        result = width_calculator.calculate_crack_width(binary, return_debug=True)
        
        print(f"宽度测量结果:")
        print(f"  测量点数: {result['width_count']}")
        if result['width_count'] > 0:
            stats = result['statistics']
            print(f"  平均宽度: {stats['mean_width']:.3f}{width_calculator.unit}")
            print(f"  最大宽度: {stats['max_width']:.3f}{width_calculator.unit}")
            print(f"  最小宽度: {stats['min_width']:.3f}{width_calculator.unit}")
            print(f"  标准差: {stats['std_width']:.3f}{width_calculator.unit}")
        
        # 可视化结果
        vis_result = width_calculator.visualize_width_results(
            image, result, save_path="output/width_result.jpg"
        )
        
        print("宽度计算完成！")
    else:
        print(f"测试图像不存在: {test_image_path}")
