
于图像处理技术的裂缝检测系统，可以自动识别图像中的裂缝并计算裂缝面积。

非最大值抑制技术提高检测精度
- 自动计算裂缝面积
- 支持批处理和单张图片处理

- Python 3.8 或更高版本
- NumPy
- Matplotlib (用于测试脚本)
## 安装依赖
```bash
```
## 使用方法
### 方法1：使用批处理文件（Windows）
1. 将图片拖放到 `run_crack_detector.bat` 文件上
   ```
   ```
### 方法2：使用Python命令行
```bash
```
参数说明：
- `--output`：输出目录（可选，默认为输入图片所在目录）



python test_crack_detector.py


run_test.bat


2. 边缘检测：使用Sobel算子计算梯度
4. 非最大值抑制：提高边缘定位精度
6. 结果可视化：在原图上标记裂缝并计算面积
## 参数调整
可以在 `CrackDetector` 类的 `__init__` 方法中调整以下参数：
- `fudgefactor`：阈值调整因子（默认1.8）
- `with_nmsup`：是否使用非最大值抑制（默认True）
## 示例
```python

detector = CrackDetector("path/to/image.jpg")
# 检测裂缝

detector.save_result(result_image, "output_directory")
# 打印裂缝面积
```