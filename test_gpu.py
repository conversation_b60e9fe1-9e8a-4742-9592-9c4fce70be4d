# -*- coding: utf-8 -*-
"""
简单的GPU测试脚本
"""

print("=== GPU测试开始 ===")

try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        print(f"✅ 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 测试GPU计算
        x = torch.randn(1000, 1000).cuda()
        y = torch.randn(1000, 1000).cuda()
        z = torch.mm(x, y)
        print("✅ GPU计算测试成功")
    
except Exception as e:
    print(f"❌ PyTorch测试失败: {e}")

try:
    from ultralytics import YOLO
    print("✅ Ultralytics导入成功")
    
    # 测试模型加载
    model = YOLO('yolo11n.pt')
    print("✅ YOLO模型加载成功")
    
except Exception as e:
    print(f"❌ Ultralytics测试失败: {e}")

print("=== GPU测试完成 ===")
