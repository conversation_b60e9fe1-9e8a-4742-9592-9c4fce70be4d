# -*- coding: utf-8 -*-
"""
简化的GPU训练脚本
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')

# 直接导入，不做复杂检查
import torch
from ultralytics import YOLO

def main():
    """主训练函数"""
    print("=== GPU YOLO裂缝检测模型训练 ===")
    
    # 检查GPU
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        print(f"GPU数量: {device_count}")
        
        for i in range(device_count):
            gpu_name = torch.cuda.get_device_name(i)
            memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"  GPU {i}: {gpu_name} ({memory:.1f} GB)")
        
        # GPU配置
        device = '0'
        batch_size = 16  # RTX A5000 24GB可以支持大批次
        workers = 8
        cache = True
        amp = True
        optimizer = 'AdamW'
        print("🚀 使用GPU训练配置")
    else:
        # CPU配置
        device = 'cpu'
        batch_size = 2
        workers = 0
        cache = False
        amp = False
        optimizer = 'SGD'
        print("💻 使用CPU训练配置")
    
    # 加载模型
    model_path = r'E:\yolo\ultralytics-main\ultralytics-main-max-area\yolo11n-seg.pt'
    if not os.path.exists(model_path):
        print("⚠️  本地模型文件不存在，将自动下载")
        model = YOLO('yolo11n-seg.pt')
    else:
        model = YOLO(model_path)
    
    print(f"\n🚀 开始训练...")
    print(f"设备: {device}")
    print(f"批次大小: {batch_size}")
    print(f"工作进程: {workers}")
    print(f"混合精度: {amp}")
    print(f"优化器: {optimizer}")
    
    # 训练参数
    train_args = {
        'data': r'E:\yolo\ultralytics-main\ultralytics-main-max-area\data.yaml',
        'imgsz': 640,
        'epochs': 300,
        'batch': batch_size,
        'workers': workers,
        'device': device,
        'optimizer': optimizer,
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'close_mosaic': 10,
        'resume': False,
        'project': 'runs/train',
        'name': 'crack_detection_gpu',
        'single_cls': False,
        'cache': cache,
        'amp': amp,
        'save_period': 10,
        'patience': 50,
        'verbose': True,
        'plots': True,
        'val': True,
    }
    
    try:
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 开始训练
        results = model.train(**train_args)
        
        print("✅ 训练完成!")
        print(f"最佳模型保存在: {results.save_dir}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == '__main__':
    main()
