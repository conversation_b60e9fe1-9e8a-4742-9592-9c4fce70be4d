# 增强版裂缝检测系统 - 安装指南

## 🚀 快速安装

### 方法一：自动安装脚本（推荐）

#### Windows用户
```bash
# 双击运行
install.bat

# 或在命令行运行
python install_dependencies.py
```

#### Linux/macOS用户
```bash
# 给脚本执行权限并运行
chmod +x install.sh
./install.sh

# 或直接运行Python脚本
python3 install_dependencies.py
```

### 方法二：手动安装

#### 1. 检查Python版本
确保您的Python版本为3.7或更高：
```bash
python --version
# 或
python3 --version
```

#### 2. 升级pip
```bash
python -m pip install --upgrade pip
```

#### 3. 安装依赖包
```bash
pip install -r requirements_enhanced.txt
```

#### 4. 验证安装
```bash
python quick_start.py
```

## 📦 依赖包说明

### 核心依赖（必需）
- **numpy**: 数值计算库
- **opencv-python**: 图像处理库
- **matplotlib**: 图形绘制库
- **scikit-image**: 图像处理算法
- **scikit-learn**: 机器学习库
- **scipy**: 科学计算库
- **pandas**: 数据处理库

### GUI界面（可选）
- **PyQt5**: 图形用户界面

### 深度学习（可选）
- **ultralytics**: YOLO模型支持

### 测试框架（开发用）
- **pytest**: 单元测试框架

## 🔧 常见问题解决

### 问题1：Python未安装或版本过低
**解决方案：**
- Windows: 从 [python.org](https://www.python.org/downloads/) 下载安装
- Ubuntu/Debian: `sudo apt-get install python3 python3-pip`
- CentOS/RHEL: `sudo yum install python3 python3-pip`
- macOS: `brew install python3`

### 问题2：pip安装失败
**解决方案：**
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements_enhanced.txt

# 或使用阿里云镜像
pip install -i https://mirrors.aliyun.com/pypi/simple/ -r requirements_enhanced.txt
```

### 问题3：OpenCV安装失败
**解决方案：**
```bash
# 尝试不同的OpenCV包
pip install opencv-python-headless

# 或安装完整版本
pip install opencv-contrib-python
```

### 问题4：PyQt5安装失败
**解决方案：**
```bash
# Ubuntu/Debian需要额外包
sudo apt-get install python3-pyqt5

# 或尝试PyQt6
pip install PyQt6
```

### 问题5：权限错误
**解决方案：**
```bash
# 使用用户安装
pip install --user -r requirements_enhanced.txt

# 或使用虚拟环境
python -m venv crack_detection_env
source crack_detection_env/bin/activate  # Linux/macOS
# crack_detection_env\Scripts\activate  # Windows
pip install -r requirements_enhanced.txt
```

## 🌐 网络问题解决

### 使用国内镜像源
如果网络连接较慢，可以使用国内镜像源：

```bash
# 清华大学镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements_enhanced.txt

# 阿里云镜像
pip install -i https://mirrors.aliyun.com/pypi/simple/ -r requirements_enhanced.txt

# 中科大镜像
pip install -i https://pypi.mirrors.ustc.edu.cn/simple/ -r requirements_enhanced.txt
```

### 永久配置镜像源
创建pip配置文件：

**Windows**: `%APPDATA%\pip\pip.ini`
**Linux/macOS**: `~/.pip/pip.conf`

内容：
```ini
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
```

## 🐳 Docker安装（高级用户）

如果您熟悉Docker，可以使用容器化安装：

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements_enhanced.txt .
RUN pip install -r requirements_enhanced.txt

COPY . .
CMD ["python", "quick_start.py"]
```

构建和运行：
```bash
docker build -t crack-detection .
docker run -it --rm -v $(pwd)/output:/app/output crack-detection
```

## 🧪 验证安装

运行以下命令验证安装是否成功：

```bash
# 1. 运行快速启动脚本
python quick_start.py

# 2. 运行测试脚本
python test_enhanced_system.py

# 3. 检查模块导入
python -c "
import cv2, numpy, matplotlib, sklearn, skimage
print('✅ 所有核心模块导入成功')
"

# 4. 测试GUI界面（如果安装了PyQt5）
python enhanced_crack_gui.py

# 5. 查看命令行帮助
python enhanced_crack_detect.py --help
```

## 📱 移动端/Web版本

目前系统主要支持桌面环境。如果需要移动端或Web版本，可以考虑：

1. **Streamlit Web应用**: 使用Streamlit创建Web界面
2. **Flask/Django**: 创建Web服务
3. **Kivy**: 创建跨平台移动应用

## 🆘 获取帮助

如果遇到安装问题：

1. **查看错误日志**: 仔细阅读错误信息
2. **检查网络连接**: 确保能访问PyPI
3. **更新系统**: 确保操作系统和Python是最新版本
4. **虚拟环境**: 在干净的虚拟环境中安装
5. **联系支持**: 提供详细的错误信息和系统环境

## 📋 安装检查清单

- [ ] Python 3.7+ 已安装
- [ ] pip 已安装并升级到最新版本
- [ ] 网络连接正常
- [ ] 所有核心依赖包已安装
- [ ] GUI包已安装（如需图形界面）
- [ ] YOLO包已安装（如需深度学习功能）
- [ ] 测试图像和输出目录已创建
- [ ] 系统验证测试通过

完成以上检查后，您就可以开始使用增强版裂缝检测系统了！

---

**安装完成后，建议首先运行 `python quick_start.py` 来熟悉系统功能。**
