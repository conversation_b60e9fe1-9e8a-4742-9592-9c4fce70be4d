@echo off
echo === Starting RTX A5000 GPU Training ===
echo.

echo Activating conda environment...
call conda activate ultralytics-main-max-area

echo.
echo Checking GPU status...
python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA:', torch.cuda.is_available()); print('GPU:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None')"

echo.
echo Starting training...
python train_gpu_rtx_a5000.py

echo.
echo Training completed!
pause
