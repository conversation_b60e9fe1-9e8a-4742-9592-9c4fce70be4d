#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版裂缝检测系统测试脚本

测试功能:
1. 基础检测功能测试
2. 参数设置测试
3. 宽度计算测试
4. 批量处理测试
5. GUI组件测试
"""

import os
import sys
import unittest
import numpy as np
import cv2
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crack_detection import EnhancedCrackDetector, CrackDetector
from crack_width_calculator import CrackWidthCalculator


class TestEnhancedCrackDetection(unittest.TestCase):
    """增强版裂缝检测系统测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.test_image_dir = "test_images"
        self.output_dir = "test_output"
        
        # 创建测试目录
        os.makedirs(self.test_image_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 创建测试图像
        self.create_test_images()
        
        # 初始化检测器
        self.detector = EnhancedCrackDetector(
            scale_factor=0.1,
            unit='mm²'
        )
    
    def create_test_images(self):
        """创建测试图像"""
        # 创建简单的测试裂缝图像
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image.fill(255)  # 白色背景
        
        # 绘制模拟裂缝
        cv2.line(test_image, (100, 100), (500, 300), (0, 0, 0), 3)  # 黑色裂缝
        cv2.line(test_image, (200, 50), (400, 350), (0, 0, 0), 2)
        
        # 添加一些噪声
        cv2.circle(test_image, (300, 200), 5, (0, 0, 0), -1)
        
        # 保存测试图像
        self.test_image_path = os.path.join(self.test_image_dir, "test_crack.jpg")
        cv2.imwrite(self.test_image_path, test_image)
        
        # 创建第二张测试图像
        test_image2 = np.zeros((300, 400, 3), dtype=np.uint8)
        test_image2.fill(255)
        cv2.line(test_image2, (50, 150), (350, 150), (0, 0, 0), 4)
        
        self.test_image_path2 = os.path.join(self.test_image_dir, "test_crack2.jpg")
        cv2.imwrite(self.test_image_path2, test_image2)
    
    def test_detector_initialization(self):
        """测试检测器初始化"""
        print("\n=== 测试检测器初始化 ===")
        
        # 测试基本初始化
        detector = EnhancedCrackDetector()
        self.assertIsNotNone(detector)
        self.assertEqual(detector.scale_factor, 1.0)
        self.assertEqual(detector.unit, 'mm²')
        
        # 测试参数初始化
        detector = EnhancedCrackDetector(scale_factor=0.05, unit='cm²')
        self.assertEqual(detector.scale_factor, 0.05)
        self.assertEqual(detector.unit, 'cm²')
        
        print("✅ 检测器初始化测试通过")
    
    def test_parameter_setting(self):
        """测试参数设置"""
        print("\n=== 测试参数设置 ===")
        
        # 测试比例尺设置
        self.detector.set_scale_factor(0.2)
        self.assertEqual(self.detector.scale_factor, 0.2)
        
        # 测试单位设置
        self.detector.set_unit('cm²')
        self.assertEqual(self.detector.unit, 'cm²')
        
        # 测试预处理参数更新
        self.detector.update_preprocessing_params(gamma=1.5, median_kernel=7)
        self.assertEqual(self.detector.preprocessing_params['gamma'], 1.5)
        self.assertEqual(self.detector.preprocessing_params['median_kernel'], 7)
        
        # 测试轮廓筛选参数更新
        self.detector.update_contour_filter_params(min_area=100, max_area=20000)
        self.assertEqual(self.detector.contour_filter_params['min_area'], 100)
        self.assertEqual(self.detector.contour_filter_params['max_area'], 20000)
        
        print("✅ 参数设置测试通过")
    
    def test_image_preprocessing(self):
        """测试图像预处理"""
        print("\n=== 测试图像预处理 ===")
        
        # 读取测试图像
        image = cv2.imread(self.test_image_path)
        self.assertIsNotNone(image)
        
        # 测试多层次预处理
        processed_image, process_steps = self.detector.multilevel_preprocess(image)
        
        # 检查处理步骤
        expected_steps = ['1_grayscale', '2_gamma_corrected', '3_median_filtered', 
                         '4_bilateral_filtered', '5_clahe_enhanced']
        for step in expected_steps:
            self.assertIn(step, process_steps)
            self.assertIsNotNone(process_steps[step])
        
        # 检查输出图像
        self.assertIsNotNone(processed_image)
        self.assertEqual(len(processed_image.shape), 2)  # 应该是灰度图像
        
        print("✅ 图像预处理测试通过")
    
    def test_crack_detection(self):
        """测试裂缝检测"""
        print("\n=== 测试裂缝检测 ===")
        
        # 读取测试图像
        image = cv2.imread(self.test_image_path)
        
        # 测试检测功能
        contours, result_image, debug_images, filter_info = self.detector.detect_cracks(
            image, return_debug_images=True, use_yolo=False
        )
        
        # 检查结果
        self.assertIsNotNone(contours)
        self.assertIsNotNone(result_image)
        self.assertIsNotNone(debug_images)
        self.assertIsNotNone(filter_info)
        
        # 检查调试图像
        self.assertIn('1_grayscale', debug_images)
        self.assertIn('binary', debug_images)
        self.assertIn('edges', debug_images)
        
        print(f"检测到 {len(contours)} 个轮廓")
        print("✅ 裂缝检测测试通过")
    
    def test_area_calculation(self):
        """测试面积计算"""
        print("\n=== 测试面积计算 ===")
        
        # 创建测试轮廓
        contour1 = np.array([[100, 100], [200, 100], [200, 200], [100, 200]], dtype=np.int32)
        contour2 = np.array([[300, 300], [400, 300], [400, 400], [300, 400]], dtype=np.int32)
        contours = [contour1, contour2]
        
        # 计算面积
        area_info = self.detector.calculate_precise_area(contours)
        
        # 检查结果
        self.assertEqual(area_info['contour_count'], 2)
        self.assertGreater(area_info['total_pixel_area'], 0)
        self.assertGreater(area_info['total_real_area'], 0)
        self.assertEqual(len(area_info['individual_pixel_areas']), 2)
        self.assertEqual(len(area_info['individual_real_areas']), 2)
        
        # 检查统计信息
        stats = area_info['area_statistics']
        self.assertIn('mean_pixel_area', stats)
        self.assertIn('max_pixel_area', stats)
        self.assertIn('min_pixel_area', stats)
        
        print(f"总面积: {area_info['total_real_area']:.2f} {self.detector.unit}")
        print("✅ 面积计算测试通过")
    
    def test_single_image_processing(self):
        """测试单张图像处理"""
        print("\n=== 测试单张图像处理 ===")
        
        # 处理单张图像
        result = self.detector.process_single_image(
            self.test_image_path,
            output_dir=self.output_dir,
            use_yolo=False
        )
        
        # 检查结果
        self.assertNotIn('error', result)
        self.assertIn('area_info', result)
        self.assertIn('result_image', result)
        self.assertIn('processing_time', result)
        
        # 检查面积信息
        area_info = result['area_info']
        self.assertIsInstance(area_info['contour_count'], int)
        self.assertIsInstance(area_info['total_real_area'], (int, float))
        
        print(f"处理时间: {result['processing_time']:.2f}秒")
        print(f"检测到 {area_info['contour_count']} 个裂缝")
        print("✅ 单张图像处理测试通过")
    
    def test_width_calculation(self):
        """测试宽度计算"""
        print("\n=== 测试宽度计算 ===")
        
        # 创建宽度计算器
        width_calculator = CrackWidthCalculator(scale_factor=0.1, unit='mm')
        
        # 创建测试二值掩码
        mask = np.zeros((200, 300), dtype=np.uint8)
        cv2.line(mask, (50, 100), (250, 100), 255, 10)  # 水平线
        
        # 计算宽度
        width_result = width_calculator.calculate_crack_width(mask, return_debug=True)
        
        # 检查结果
        self.assertIn('width_count', width_result)
        self.assertIn('statistics', width_result)
        self.assertIn('debug_info', width_result)
        
        if width_result['width_count'] > 0:
            stats = width_result['statistics']
            self.assertIn('mean_width', stats)
            self.assertIn('max_width', stats)
            self.assertIn('min_width', stats)
            print(f"宽度测量点数: {width_result['width_count']}")
            print(f"平均宽度: {stats['mean_width']:.3f} mm")
        
        print("✅ 宽度计算测试通过")
    
    def test_batch_processing(self):
        """测试批量处理"""
        print("\n=== 测试批量处理 ===")
        
        # 批量处理测试图像
        results = self.detector.batch_process_images(
            self.test_image_dir,
            output_dir=self.output_dir,
            use_yolo=False,
            generate_csv=True
        )
        
        # 检查结果
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        
        # 检查每个结果
        for result in results:
            if 'error' not in result:
                self.assertIn('area_info', result)
                self.assertIn('image_path', result)
        
        # 统计成功率
        successful = sum(1 for r in results if 'error' not in r)
        print(f"批量处理: {successful}/{len(results)} 成功")
        print("✅ 批量处理测试通过")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        print("\n=== 测试向后兼容性 ===")
        
        # 测试旧版本CrackDetector类
        old_detector = CrackDetector(scale_factor=0.1, unit='mm²')
        
        # 读取测试图像
        image = cv2.imread(self.test_image_path)
        
        # 测试旧版本方法
        contours, result = old_detector.detect_cracks(image)
        pixel_area, real_area, contour_areas = old_detector.calculate_area(contours)
        
        # 检查结果
        self.assertIsNotNone(contours)
        self.assertIsNotNone(result)
        self.assertIsInstance(pixel_area, (int, float))
        self.assertIsInstance(real_area, (int, float))
        self.assertIsInstance(contour_areas, list)
        
        print("✅ 向后兼容性测试通过")
    
    def tearDown(self):
        """测试清理"""
        # 清理测试文件（可选）
        pass


def run_performance_test():
    """性能测试"""
    print("\n" + "="*50)
    print("性能测试")
    print("="*50)
    
    import time
    
    # 创建检测器
    detector = EnhancedCrackDetector(scale_factor=0.1, unit='mm²')
    
    # 创建测试图像
    test_image = np.zeros((800, 1200, 3), dtype=np.uint8)
    test_image.fill(255)
    
    # 绘制复杂裂缝
    for i in range(5):
        start_point = (100 + i*200, 100 + i*50)
        end_point = (1000 - i*100, 700 - i*100)
        cv2.line(test_image, start_point, end_point, (0, 0, 0), 2 + i)
    
    test_path = "performance_test.jpg"
    cv2.imwrite(test_path, test_image)
    
    # 性能测试
    start_time = time.time()
    result = detector.process_single_image(test_path, use_yolo=False)
    end_time = time.time()
    
    if 'error' not in result:
        area_info = result['area_info']
        print(f"图像尺寸: 800x1200")
        print(f"处理时间: {end_time - start_time:.2f}秒")
        print(f"检测到裂缝数: {area_info['contour_count']}")
        print(f"总面积: {area_info['total_real_area']:.2f} mm²")
    
    # 清理
    if os.path.exists(test_path):
        os.remove(test_path)


def main():
    """主测试函数"""
    print("增强版裂缝检测系统测试")
    print("="*50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n" + "="*50)
    print("所有测试完成！")
    print("="*50)


if __name__ == "__main__":
    main()
