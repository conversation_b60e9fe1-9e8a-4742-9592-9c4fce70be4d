# -*- coding: utf-8 -*-
"""
GPU环境设置脚本
"""

import subprocess
import sys
import os

def run_cmd(cmd, description):
    """运行命令"""
    print(f"\n🔄 {description}...")
    print(f"命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        return False

def main():
    """主函数"""
    print("=== GPU环境设置 ===")
    
    # 检查conda
    try:
        subprocess.run(['conda', '--version'], check=True, capture_output=True)
        print("✅ Conda可用")
    except:
        print("❌ Conda不可用，请先安装Anaconda或Miniconda")
        return
    
    # 设置步骤
    steps = [
        ('conda remove -n yolo-gpu --all -y', '删除旧环境'),
        ('conda create -n yolo-gpu python=3.10 -y', '创建新环境'),
    ]
    
    for cmd, desc in steps:
        if not run_cmd(cmd, desc):
            print(f"❌ 环境设置失败")
            return
    
    print("\n✅ 基础环境创建完成")
    print("\n📋 接下来请手动执行以下步骤:")
    print("1. conda activate yolo-gpu")
    print("2. pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
    print("3. pip install ultralytics")
    print("4. python train_gpu_simple.py")

if __name__ == '__main__':
    main()
