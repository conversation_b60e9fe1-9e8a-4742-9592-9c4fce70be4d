# -*- coding: utf-8 -*-
"""
高级分割系统 - 优化的YOLO分割模型选择和面积计算

功能：
1. 智能模型选择 - 根据图像特征自动选择最佳分割模型
2. 精确面积计算 - 基于分割掩码的高精度面积计算
3. 多尺度分析 - 支持不同分辨率的分割和计算
4. 质量评估 - 分割质量自动评估和优化建议
"""

import cv2
import numpy as np
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import json
import time
from datetime import datetime

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False

class AdvancedSegmentationSystem:
    """高级分割系统"""
    
    def __init__(self, scale_factor: float = 0.1, unit: str = 'mm²'):
        """
        初始化高级分割系统
        
        Args:
            scale_factor: 像素到实际单位的转换比例
            unit: 面积单位
        """
        self.scale_factor = scale_factor
        self.unit = unit
        
        # 可用的分割模型
        self.available_models = {
            'yolo11n-seg': {
                'path': 'yolo11n-seg.pt',
                'size': 'nano',
                'speed': 'fast',
                'accuracy': 'medium',
                'description': '轻量级分割模型，速度快'
            },
            'yolo11s-seg': {
                'path': 'yolo11s-seg.pt', 
                'size': 'small',
                'speed': 'medium',
                'accuracy': 'good',
                'description': '小型分割模型，平衡速度和精度'
            },
            'yolo11m-seg': {
                'path': 'yolo11m-seg.pt',
                'size': 'medium', 
                'speed': 'medium',
                'accuracy': 'high',
                'description': '中型分割模型，高精度'
            },
            'yolo11l-seg': {
                'path': 'yolo11l-seg.pt',
                'size': 'large',
                'speed': 'slow',
                'accuracy': 'very_high', 
                'description': '大型分割模型，最高精度'
            }
        }
        
        self.current_model = None
        self.current_model_name = None
        
        # 自动选择最佳可用模型
        self.auto_select_model()
    
    def auto_select_model(self) -> str:
        """自动选择最佳可用模型"""
        if not YOLO_AVAILABLE:
            print("❌ YOLO不可用，无法使用分割功能")
            return None
        
        # 按优先级检查模型
        priority_order = ['yolo11n-seg', 'yolo11s-seg', 'yolo11m-seg', 'yolo11l-seg']
        
        for model_name in priority_order:
            model_info = self.available_models[model_name]
            if os.path.exists(model_info['path']):
                try:
                    self.current_model = YOLO(model_info['path'])
                    self.current_model_name = model_name
                    print(f"✅ 已加载模型: {model_name} - {model_info['description']}")
                    return model_name
                except Exception as e:
                    print(f"❌ 模型加载失败: {model_name} - {e}")
                    continue
        
        # 如果本地没有模型，尝试下载
        try:
            print("📥 正在下载默认分割模型...")
            self.current_model = YOLO('yolo11n-seg.pt')
            self.current_model_name = 'yolo11n-seg'
            print("✅ 默认模型下载并加载成功")
            return 'yolo11n-seg'
        except Exception as e:
            print(f"❌ 模型下载失败: {e}")
            return None
    
    def select_model(self, model_name: str) -> bool:
        """手动选择分割模型"""
        if model_name not in self.available_models:
            print(f"❌ 未知模型: {model_name}")
            return False
        
        model_info = self.available_models[model_name]
        
        try:
            self.current_model = YOLO(model_info['path'])
            self.current_model_name = model_name
            print(f"✅ 已切换到模型: {model_name} - {model_info['description']}")
            return True
        except Exception as e:
            print(f"❌ 模型切换失败: {model_name} - {e}")
            return False
    
    def analyze_image_characteristics(self, image: np.ndarray) -> Dict:
        """分析图像特征，用于模型选择建议"""
        height, width = image.shape[:2]
        
        # 计算图像复杂度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        
        # 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (height * width)
        
        # 纹理复杂度
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # 对比度
        contrast = gray.std()
        
        characteristics = {
            'resolution': f"{width}x{height}",
            'total_pixels': width * height,
            'edge_density': edge_density,
            'texture_complexity': laplacian_var,
            'contrast': contrast,
            'complexity_level': self._classify_complexity(edge_density, laplacian_var, contrast)
        }
        
        return characteristics
    
    def _classify_complexity(self, edge_density: float, texture_complexity: float, contrast: float) -> str:
        """分类图像复杂度"""
        complexity_score = (edge_density * 100 + texture_complexity / 1000 + contrast / 100) / 3
        
        if complexity_score < 10:
            return 'low'
        elif complexity_score < 25:
            return 'medium'
        elif complexity_score < 50:
            return 'high'
        else:
            return 'very_high'
    
    def recommend_model(self, image: np.ndarray) -> str:
        """根据图像特征推荐最佳模型"""
        characteristics = self.analyze_image_characteristics(image)
        
        complexity = characteristics['complexity_level']
        total_pixels = characteristics['total_pixels']
        
        # 根据复杂度和图像大小推荐模型
        if complexity == 'low' and total_pixels < 1000000:  # 1MP
            return 'yolo11n-seg'
        elif complexity == 'medium' and total_pixels < 2000000:  # 2MP
            return 'yolo11s-seg'
        elif complexity == 'high' or total_pixels > 2000000:
            return 'yolo11m-seg'
        else:
            return 'yolo11l-seg'
    
    def segment_cracks(self, image: np.ndarray, confidence_threshold: float = 0.25) -> Tuple[np.ndarray, Dict]:
        """
        执行裂缝分割
        
        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            
        Returns:
            分割掩码和详细信息
        """
        if self.current_model is None:
            return None, {'error': '没有可用的分割模型'}
        
        start_time = time.time()
        
        try:
            # 执行分割
            results = self.current_model.predict(
                image, 
                conf=confidence_threshold,
                verbose=False
            )
            
            if not results or len(results) == 0:
                return None, {'error': '分割失败，没有检测结果'}
            
            result = results[0]
            
            # 提取分割掩码
            if result.masks is None:
                # YOLO分割失败，尝试传统方法
                print("⚠️  YOLO分割未检测到掩码，回退到传统检测方法")
                return self._fallback_to_traditional_detection(image, confidence_threshold)
            
            # 合并所有掩码
            masks = result.masks.data.cpu().numpy()
            confidences = result.boxes.conf.cpu().numpy() if result.boxes else []
            
            combined_mask = np.zeros((image.shape[0], image.shape[1]), dtype=np.uint8)
            
            for i, mask in enumerate(masks):
                # 调整掩码尺寸
                mask_resized = cv2.resize(mask, (image.shape[1], image.shape[0]))
                mask_binary = (mask_resized > 0.5).astype(np.uint8) * 255
                combined_mask = np.maximum(combined_mask, mask_binary)
            
            processing_time = time.time() - start_time
            
            # 分割质量评估
            quality_metrics = self._evaluate_segmentation_quality(combined_mask, image)
            
            segmentation_info = {
                'model_used': self.current_model_name,
                'processing_time': processing_time,
                'confidence_threshold': confidence_threshold,
                'num_detections': len(masks),
                'average_confidence': np.mean(confidences) if len(confidences) > 0 else 0,
                'quality_metrics': quality_metrics
            }
            
            return combined_mask, segmentation_info
            
        except Exception as e:
            print(f"⚠️  YOLO分割出错: {e}，回退到传统检测方法")
            return self._fallback_to_traditional_detection(image, confidence_threshold)

    def _fallback_to_traditional_detection(self, image: np.ndarray, confidence_threshold: float) -> Tuple[np.ndarray, Dict]:
        """传统检测方法作为回退方案"""
        try:
            start_time = time.time()

            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 图像预处理
            # 1. Gamma校正增强对比度
            gamma = 1.5
            gamma_corrected = np.power(gray / 255.0, gamma) * 255.0
            gamma_corrected = gamma_corrected.astype(np.uint8)

            # 2. 双边滤波去噪
            bilateral = cv2.bilateralFilter(gamma_corrected, 9, 80, 80)

            # 3. 自适应阈值
            adaptive_thresh = cv2.adaptiveThreshold(
                bilateral, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY_INV, 9, 2
            )

            # 4. 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            morphed = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
            morphed = cv2.morphologyEx(morphed, cv2.MORPH_OPEN, kernel)

            # 5. 轮廓筛选
            contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 创建掩码
            mask = np.zeros(gray.shape, dtype=np.uint8)

            valid_contours = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 20:  # 最小面积阈值
                    # 计算圆形度
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter * perimeter)
                        if circularity < 0.9:  # 过滤圆形噪声
                            cv2.fillPoly(mask, [contour], 255)
                            valid_contours += 1

            processing_time = time.time() - start_time

            # 评估传统检测质量
            quality_metrics = self._evaluate_segmentation_quality(mask, image)

            segmentation_info = {
                'model_used': 'traditional_fallback',
                'processing_time': processing_time,
                'confidence_threshold': confidence_threshold,
                'num_detections': valid_contours,
                'average_confidence': 0.8,  # 传统方法的默认置信度
                'quality_metrics': quality_metrics,
                'fallback_reason': 'YOLO分割失败，使用传统方法'
            }

            if np.sum(mask) > 0:
                print(f"✅ 传统方法检测成功，找到 {valid_contours} 个裂缝区域")
                return mask, segmentation_info
            else:
                return None, {'error': '传统方法也未检测到裂缝，请检查图像质量或调整参数'}

        except Exception as e:
            return None, {'error': f'传统检测方法失败: {str(e)}'}

    def _evaluate_segmentation_quality(self, mask: np.ndarray, image: np.ndarray) -> Dict:
        """评估分割质量"""
        # 计算分割区域的连通性
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(mask, connectivity=8)
        
        # 计算分割区域的形状特征
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if len(contours) == 0:
            return {'quality_score': 0, 'issues': ['没有检测到有效轮廓']}
        
        # 计算质量指标
        total_area = np.sum(mask > 0)
        num_components = num_labels - 1  # 减去背景
        
        # 形状规整度
        shape_regularity = []
        for contour in contours:
            if cv2.contourArea(contour) > 10:
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * cv2.contourArea(contour) / (perimeter * perimeter)
                    shape_regularity.append(1 - circularity)  # 裂缝应该是非圆形的
        
        avg_shape_regularity = np.mean(shape_regularity) if shape_regularity else 0
        
        # 连通性评分
        connectivity_score = min(1.0, num_components / max(1, len(contours)))
        
        # 覆盖率评分
        coverage_score = min(1.0, total_area / (image.shape[0] * image.shape[1] * 0.1))
        
        # 综合质量评分
        quality_score = (avg_shape_regularity * 0.4 + connectivity_score * 0.3 + coverage_score * 0.3)
        
        issues = []
        if quality_score < 0.3:
            issues.append('分割质量较低')
        if num_components > len(contours) * 2:
            issues.append('分割过于碎片化')
        if total_area < 100:
            issues.append('检测到的裂缝区域过小')
        
        return {
            'quality_score': quality_score,
            'total_area_pixels': total_area,
            'num_components': num_components,
            'num_contours': len(contours),
            'avg_shape_regularity': avg_shape_regularity,
            'connectivity_score': connectivity_score,
            'coverage_score': coverage_score,
            'issues': issues
        }
    
    def calculate_precise_area(self, mask: np.ndarray) -> Dict:
        """
        基于分割掩码计算精确面积
        
        Args:
            mask: 二值分割掩码
            
        Returns:
            详细的面积计算结果
        """
        if mask is None or np.sum(mask) == 0:
            return {
                'total_pixel_area': 0,
                'total_real_area': 0,
                'contour_count': 0,
                'individual_areas': [],
                'area_statistics': {
                    'mean_area': 0,
                    'std_area': 0,
                    'min_area': 0,
                    'max_area': 0
                }
            }
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {
                'total_pixel_area': 0,
                'total_real_area': 0,
                'contour_count': 0,
                'individual_areas': [],
                'area_statistics': {
                    'mean_area': 0,
                    'std_area': 0,
                    'min_area': 0,
                    'max_area': 0
                }
            }
        
        # 计算每个轮廓的面积
        pixel_areas = []
        real_areas = []
        
        for contour in contours:
            pixel_area = cv2.contourArea(contour)
            if pixel_area > 0:  # 过滤掉面积为0的轮廓
                real_area = pixel_area * (self.scale_factor ** 2)
                pixel_areas.append(pixel_area)
                real_areas.append(real_area)
        
        if not real_areas:
            return {
                'total_pixel_area': 0,
                'total_real_area': 0,
                'contour_count': 0,
                'individual_areas': [],
                'area_statistics': {
                    'mean_area': 0,
                    'std_area': 0,
                    'min_area': 0,
                    'max_area': 0
                }
            }
        
        # 计算统计信息
        total_pixel_area = sum(pixel_areas)
        total_real_area = sum(real_areas)
        
        area_statistics = {
            'mean_area': np.mean(real_areas),
            'std_area': np.std(real_areas),
            'min_area': np.min(real_areas),
            'max_area': np.max(real_areas),
            'median_area': np.median(real_areas)
        }
        
        return {
            'total_pixel_area': total_pixel_area,
            'total_real_area': total_real_area,
            'contour_count': len(real_areas),
            'individual_pixel_areas': pixel_areas,
            'individual_real_areas': real_areas,
            'area_statistics': area_statistics,
            'scale_factor': self.scale_factor,
            'unit': self.unit
        }
    
    def get_model_info(self) -> Dict:
        """获取当前模型信息"""
        if self.current_model_name:
            model_info = self.available_models[self.current_model_name].copy()
            model_info['current'] = True
            return model_info
        return {'current': False, 'error': '没有加载模型'}
    
    def list_available_models(self) -> Dict:
        """列出所有可用模型"""
        available = {}
        for name, info in self.available_models.items():
            available[name] = {
                **info,
                'available': os.path.exists(info['path']),
                'current': name == self.current_model_name
            }
        return available
