import cv2
import numpy as np
import scipy.ndimage
import math
import os

class CrackDetector:
    def __init__(self, image_path):
        self.image_path = image_path
        self.with_nmsup = True
        self.fudgefactor = 1.8
        self.sigma = 21
        self.kernel = 2 * math.ceil(2 * self.sigma) + 1

    def orientated_non_max_suppression(self, mag, ang):
        ang_quant = np.round(ang / (np.pi/4)) % 4
        winE = np.array([[0, 0, 0],[1, 1, 1], [0, 0, 0]])
        winSE = np.array([[1, 0, 0], [0, 1, 0], [0, 0, 1]])
        winS = np.array([[0, 1, 0], [0, 1, 0], [0, 1, 0]])
        winSW = np.array([[0, 0, 1], [0, 1, 0], [1, 0, 0]])

        magE = self.non_max_suppression(mag, winE)
        magSE = self.non_max_suppression(mag, winSE)
        magS = self.non_max_suppression(mag, winS)
        magSW = self.non_max_suppression(mag, winSW)

        mag[ang_quant == 0] = magE[ang_quant == 0]
        mag[ang_quant == 1] = magSE[ang_quant == 1]
        mag[ang_quant == 2] = magS[ang_quant == 2]
        mag[ang_quant == 3] = magSW[ang_quant == 3]
        return mag

    def non_max_suppression(self, data, win):
        data_max = scipy.ndimage.filters.maximum_filter(data, footprint=win, mode='constant')
        data_max[data != data_max] = 0
        return data_max

    def detect_cracks(self):
        gray_image = cv2.imread(self.image_path, 0)
        if gray_image is None:
            print(f"错误: 无法读取图片 {self.image_path}")
            return None, None

        gray_image = gray_image / 255.0
        blur = cv2.GaussianBlur(gray_image, (self.kernel, self.kernel), self.sigma)
        gray_image = cv2.subtract(gray_image, blur)

        sobelx = cv2.Sobel(gray_image, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray_image, cv2.CV_64F, 0, 1, ksize=3)
        mag = np.hypot(sobelx, sobely)
        ang = np.arctan2(sobely, sobelx)

        threshold = 4 * self.fudgefactor * np.mean(mag)
        mag[mag < threshold] = 0
        
        # 应用非最大值抑制
        if self.with_nmsup:
            mag = self.orientated_non_max_suppression(mag, ang)
        
        # 二值化处理
        mag_binary = np.zeros_like(mag, dtype=np.uint8)
        mag_binary[mag > 0] = 255
        
        # 形态学操作，连接断裂的裂缝
        kernel = np.ones((3, 3), np.uint8)
        mag_binary = cv2.morphologyEx(mag_binary, cv2.MORPH_CLOSE, kernel)
        
        # 计算裂缝面积
        crack_area = np.sum(mag_binary > 0)
        
        # 在原图上标记裂缝
        original_image = cv2.imread(self.image_path)
        crack_mask = np.zeros_like(original_image)
        crack_mask[:,:,0] = mag_binary  # 蓝色通道
        crack_mask[:,:,1] = 0           # 绿色通道
        crack_mask[:,:,2] = 0           # 红色通道
        
        # 叠加裂缝标记到原图
        result_image = cv2.addWeighted(original_image, 1.0, crack_mask, 0.7, 0)
        
        # 添加裂缝面积信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        text = f"裂缝面积: {crack_area} 像素"
        cv2.putText(result_image, text, (20, 40), font, 1, (0, 0, 255), 2, cv2.LINE_AA)
        
        return result_image, crack_area
    
    def save_result(self, result_image, output_dir=None):
        if result_image is None:
            return None
            
        if output_dir is None:
            output_dir = os.path.dirname(self.image_path)
            
        os.makedirs(output_dir, exist_ok=True)
        
        filename = os.path.basename(self.image_path)
        base_name, ext = os.path.splitext(filename)
        output_path = os.path.join(output_dir, f"{base_name}_crack_detected{ext}")
        
        cv2.imwrite(output_path, result_image)
        print(f"结果已保存至: {output_path}")
        return output_path


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="基于图像处理的裂缝检测")
    parser.add_argument("--image", type=str, required=True, help="输入图片路径")
    parser.add_argument("--output", type=str, default=None, help="输出目录")
    parser.add_argument("--show", action="store_true", help="显示结果")
    args = parser.parse_args()
    
    detector = CrackDetector(args.image)
    result_image, crack_area = detector.detect_cracks()
    
    if result_image is not None:
        output_path = detector.save_result(result_image, args.output)
        print(f"检测到的裂缝面积: {crack_area} 像素")
        
        if args.show:
            cv2.imshow("裂缝检测", result_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()