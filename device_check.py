# -*- coding: utf-8 -*-
"""
设备检测和配置脚本

功能：
1. 检测系统可用的计算设备（CPU/GPU）
2. 提供设备配置建议
3. 更新ultralytics到最新版本
4. 优化训练参数
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️  警告: 建议使用Python 3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_torch_cuda():
    """检查PyTorch和CUDA"""
    try:
        import torch
        print(f"\nPyTorch版本: {torch.__version__}")
        
        # 检查CUDA
        cuda_available = torch.cuda.is_available()
        print(f"CUDA可用: {cuda_available}")
        
        if cuda_available:
            device_count = torch.cuda.device_count()
            print(f"CUDA设备数量: {device_count}")
            
            for i in range(device_count):
                device_name = torch.cuda.get_device_name(i)
                memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  设备 {i}: {device_name} ({memory:.1f} GB)")
            
            return 'cuda', device_count
        else:
            print("CUDA不可用，将使用CPU")
            return 'cpu', 0
            
    except ImportError:
        print("❌ PyTorch未安装")
        return None, 0

def check_ultralytics():
    """检查ultralytics版本"""
    try:
        from ultralytics import __version__
        print(f"\nUltralytics版本: {__version__}")
        
        # 检查是否有更新
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--outdated'], 
                                  capture_output=True, text=True, timeout=30)
            if 'ultralytics' in result.stdout:
                print("⚠️  有新版本的ultralytics可用")
                return False
            else:
                print("✅ ultralytics是最新版本")
                return True
        except:
            print("⚠️  无法检查ultralytics更新")
            return True
            
    except ImportError:
        print("❌ ultralytics未安装")
        return False

def update_ultralytics():
    """更新ultralytics到最新版本"""
    print("\n🔄 更新ultralytics...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-U', 'ultralytics'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ ultralytics更新成功")
            return True
        else:
            print(f"❌ ultralytics更新失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 更新超时")
        return False
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def get_recommended_config(device_type, device_count=0):
    """获取推荐的训练配置"""
    config = {
        'device': 'cpu',
        'batch_size': 1,
        'workers': 0,
        'imgsz': 640,
        'epochs': 100,
        'optimizer': 'SGD'
    }
    
    if device_type == 'cuda' and device_count > 0:
        # GPU配置
        config.update({
            'device': '0' if device_count == 1 else '0,1',
            'batch_size': 8 if device_count == 1 else 16,
            'workers': 4,
            'epochs': 300
        })
        print("\n🚀 GPU训练配置:")
    else:
        # CPU配置
        config.update({
            'device': 'cpu',
            'batch_size': 2,
            'workers': 0,
            'epochs': 50  # CPU训练建议减少轮数
        })
        print("\n💻 CPU训练配置:")
    
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    return config

def create_optimized_train_script(config):
    """创建优化的训练脚本"""
    script_content = f'''# -*- coding: utf-8 -*-
"""
自动优化的YOLO训练脚本
根据系统配置自动调整参数
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')
import torch
from ultralytics import YOLO

def main():
    """主训练函数"""
    print("=== YOLO裂缝检测模型训练 ===")
    
    # 检测设备
    if torch.cuda.is_available():
        print(f"✅ 使用GPU训练: {{torch.cuda.get_device_name(0)}}")
    else:
        print("💻 使用CPU训练")
    
    # 加载模型
    model_path = r'E:\\yolo\\ultralytics-main\\ultralytics-main-max-area\\yolo11n-seg.pt'
    if not os.path.exists(model_path):
        print("⚠️  模型文件不存在，将自动下载")
        model = YOLO('yolo11n-seg.pt')
    else:
        model = YOLO(model_path)
    
    # 训练配置
    config = {{
        'data': r'E:\\yolo\\ultralytics-main\\ultralytics-main-max-area\\data.yaml',
        'imgsz': {config['imgsz']},
        'epochs': {config['epochs']},
        'batch': {config['batch_size']},
        'workers': {config['workers']},
        'device': '{config['device']}',
        'optimizer': '{config['optimizer']}',
        'close_mosaic': 10,
        'resume': False,
        'project': 'runs/train',
        'name': 'crack_detection',
        'single_cls': False,
        'cache': False,
        'save_period': 10,  # 每10轮保存一次
        'patience': 50,     # 早停耐心值
        'verbose': True
    }}
    
    print("\\n训练参数:")
    for key, value in config.items():
        print(f"  {{key}}: {{value}}")
    
    print("\\n🚀 开始训练...")
    try:
        results = model.train(**config)
        print("✅ 训练完成!")
        print(f"最佳模型保存在: {{results.save_dir}}")
    except Exception as e:
        print(f"❌ 训练失败: {{e}}")
        return False
    
    return True

if __name__ == '__main__':
    main()
'''
    
    # 保存脚本
    script_path = Path('train_optimized.py')
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"\n✅ 优化训练脚本已创建: {script_path}")
    return script_path

def check_data_yaml():
    """检查数据配置文件"""
    data_yaml = Path('data.yaml')
    if data_yaml.exists():
        print(f"\n✅ 数据配置文件存在: {data_yaml}")
        try:
            import yaml
            with open(data_yaml, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            print("数据集配置:")
            for key in ['train', 'val', 'test', 'nc', 'names']:
                if key in data:
                    print(f"  {key}: {data[key]}")
            
            return True
        except Exception as e:
            print(f"⚠️  读取数据配置失败: {e}")
            return False
    else:
        print(f"❌ 数据配置文件不存在: {data_yaml}")
        return False

def main():
    """主函数"""
    print("=== 设备检测和配置 ===")
    print(f"操作系统: {platform.system()} {platform.release()}")
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查PyTorch和CUDA
    device_type, device_count = check_torch_cuda()
    if device_type is None:
        print("❌ PyTorch未正确安装")
        return
    
    # 检查ultralytics
    ultralytics_ok = check_ultralytics()
    
    # 询问是否更新ultralytics
    if not ultralytics_ok:
        update_choice = input("\\n是否更新ultralytics到最新版本? (y/N): ").strip().lower()
        if update_choice == 'y':
            update_ultralytics()
    
    # 检查数据配置
    check_data_yaml()
    
    # 获取推荐配置
    config = get_recommended_config(device_type, device_count)
    
    # 创建优化脚本
    script_path = create_optimized_train_script(config)
    
    print("\\n=== 建议 ===")
    if device_type == 'cpu':
        print("💡 CPU训练建议:")
        print("  - 使用较小的批次大小避免内存不足")
        print("  - 减少训练轮数以节省时间")
        print("  - 考虑使用云GPU服务进行训练")
    else:
        print("🚀 GPU训练建议:")
        print("  - 可以使用更大的批次大小提高效率")
        print("  - 监控GPU内存使用情况")
        print("  - 使用混合精度训练加速")
    
    print(f"\\n🎯 使用优化脚本开始训练:")
    print(f"python {script_path}")

if __name__ == '__main__':
    main()
