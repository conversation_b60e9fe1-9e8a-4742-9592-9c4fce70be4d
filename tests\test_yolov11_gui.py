import pytest
from PyQt5.QtWidgets import QApplication
from yolov11_gui import YOLOv11G<PERSON>

@pytest.fixture(scope="module")
def qapp():
    """PyQt5应用实例"""
    app = QApplication([])
    yield app
    app.quit()

@pytest.fixture
def gui(qapp):
    """YOLOv11GUI实例"""
    window = YOLOv11GUI()
    yield window
    window.close()

def test_load_model(gui):
    """测试模型加载"""
    # 模拟模型路径
    gui.model_path_edit.setText("yolo11n-seg.pt")
    gui.load_model()
    assert gui.model is not None

def test_update_conf_value(gui):
    """测试置信度阈值更新"""
    gui.conf_slider.setValue(50)
    assert gui.conf_value_label.text() == "0.50"

def test_detect_image(gui, tmp_path):
    """测试单张图片检测"""
    # 创建临时测试图片
    test_image = tmp_path / "test.jpg"
    cv2.imwrite(str(test_image), np.zeros((100, 100, 3), dtype=np.uint8))
    
    # 模拟图片路径
    gui.image_path_edit.setText(str(test_image))
    gui.detect_image()
    
    # 验证结果
    assert gui.result_label.pixmap() is not None

@pytest.mark.skip(reason="需要实际视频文件")
def test_video_detection(gui):
    """测试视频检测"""
    pass

@pytest.mark.skip(reason="需要实际摄像头")
def test_camera_detection(gui):
    """测试摄像头检测"""
    pass