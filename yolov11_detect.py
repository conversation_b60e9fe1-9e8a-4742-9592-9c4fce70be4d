# -*- coding: utf-8 -*-
"""
YOLOv11 简化版检测脚本

不依赖PyQt5，用于测试YOLOv11模型的基本功能
支持单张图片、文件夹批量和视频检测

使用方法：
1. 图片检测: python yolov11_detect.py --source path/to/image.jpg
2. 文件夹检测: python yolov11_detect.py --source path/to/folder
3. 视频检测: python yolov11_detect.py --source path/to/video.mp4
4. 摄像头检测: python yolov11_detect.py --source 0  # 0为摄像头ID
"""

import os
import sys
import cv2
import time
import argparse
import numpy as np
from pathlib import Path
import torch

# 确保ultralytics已安装
try:
    from ultralytics import YOLO
except ImportError:
    print("请先安装ultralytics库: pip install ultralytics")
    sys.exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="YOLOv11检测脚本")
    parser.add_argument("--source", type=str, default="", help="图片/视频文件路径或文件夹路径或摄像头ID")
    parser.add_argument("--model", type=str, default="yolo11n-seg.pt", help="YOLOv11模型路径")
    parser.add_argument("--conf", type=float, default=0.25, help="置信度阈值")
    parser.add_argument("--save", action="store_true", help="是否保存检测结果")
    parser.add_argument("--show", action="store_true", help="是否显示检测结果")
    return parser.parse_args()

def detect_image(model, image_path, conf_thres=0.25, save_result=True, show_result=True):
    """检测单张图片"""
    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"错误: 无法读取图片 {image_path}")
            return
        
        # 使用YOLOv11进行检测
        results = model.predict(img, conf=conf_thres, verbose=False)[0]
        annotated_img = results.plot()
        
        # 保存结果
        if save_result:
            save_dir = os.path.join(os.path.dirname(image_path), "yolov11_results")
            os.makedirs(save_dir, exist_ok=True)
            filename = os.path.basename(image_path)
            save_path = os.path.join(save_dir, f"detected_{filename}")
            cv2.imwrite(save_path, annotated_img)
            print(f"结果已保存至: {save_path}")
        
        # 显示结果
        if show_result:
            cv2.imshow("YOLOv11 Detection", annotated_img)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            
        return annotated_img
    
    except Exception as e:
        print(f"检测过程中出错: {str(e)}")
        return None

def process_folder(model, folder_path, conf_thres=0.25, save_result=True, show_result=True):
    """批量处理文件夹中的图片"""
    try:
        # 获取所有图片文件
        image_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"]
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(Path(folder_path).glob(f"*{ext}")))
            image_files.extend(list(Path(folder_path).glob(f"*{ext.upper()}")))
        
        if not image_files:
            print(f"警告: 文件夹 {folder_path} 中没有找到图片文件")
            return
        
        # 创建保存目录
        save_dir = os.path.join(folder_path, "yolov11_results")
        if save_result:
            os.makedirs(save_dir, exist_ok=True)
        
        # 处理每张图片
        print(f"找到 {len(image_files)} 张图片，开始处理...")
        for i, img_path in enumerate(image_files):
            print(f"处理图片 {i+1}/{len(image_files)}: {img_path.name}")
            
            # 读取图片
            img = cv2.imread(str(img_path))
            if img is None:
                print(f"错误: 无法读取图片 {img_path}")
                continue
            
            # 使用YOLOv11进行检测
            results = model.predict(img, conf=conf_thres, verbose=False)[0]
            annotated_img = results.plot()
            
            # 保存结果
            if save_result:
                save_path = os.path.join(save_dir, img_path.name)
                cv2.imwrite(save_path, annotated_img)
            
            # 显示结果
            if show_result:
                cv2.imshow("YOLOv11 Detection", annotated_img)
                key = cv2.waitKey(1)
                if key == 27 or key == ord('q'):  # ESC或q键退出
                    break
        
        if show_result:
            cv2.destroyAllWindows()
        
        print(f"文件夹处理完成，结果已保存至: {save_dir}")
    
    except Exception as e:
        print(f"处理文件夹过程中出错: {str(e)}")

def process_video(model, video_path, conf_thres=0.25, save_result=True, show_result=True):
    """处理视频文件或摄像头"""
    try:
        # 打开视频源
        if video_path.isdigit() or video_path.startswith('rtsp://') or video_path.startswith('http://'):
            cap = cv2.VideoCapture(int(video_path) if video_path.isdigit() else video_path)
            source_name = f"Camera_{video_path}"
        else:
            cap = cv2.VideoCapture(video_path)
            source_name = os.path.basename(video_path)
        
        if not cap.isOpened():
            print(f"错误: 无法打开视频源 {video_path}")
            return
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 设置视频保存
        video_writer = None
        if save_result:
            if video_path.isdigit() or video_path.startswith('rtsp://') or video_path.startswith('http://'):
                # 对于摄像头，使用时间戳作为文件名
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                save_dir = "yolov11_results"
                os.makedirs(save_dir, exist_ok=True)
                save_path = os.path.join(save_dir, f"camera_{timestamp}.mp4")
            else:
                # 对于视频文件，在原目录创建结果目录
                video_dir = os.path.dirname(video_path)
                video_name = os.path.basename(video_path)
                save_dir = os.path.join(video_dir, "yolov11_results")
                os.makedirs(save_dir, exist_ok=True)
                name, ext = os.path.splitext(video_name)
                save_path = os.path.join(save_dir, f"{name}_detected{ext}")
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(save_path, fourcc, fps, (width, height))
            print(f"检测结果将保存至: {save_path}")
        
        print(f"开始处理视频: {source_name}")
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 使用YOLOv11进行检测
            results = model.predict(frame, conf=conf_thres, verbose=False)[0]
            annotated_frame = results.plot()
            
            # 保存视频
            if video_writer is not None:
                video_writer.write(annotated_frame)
            
            # 显示结果
            if show_result:
                cv2.imshow("YOLOv11 Detection", annotated_frame)
                key = cv2.waitKey(1)
                if key == 27 or key == ord('q'):  # ESC或q键退出
                    break
            
            frame_count += 1
            
            # 每100帧显示一次FPS
            if frame_count % 100 == 0:
                elapsed_time = time.time() - start_time
                fps_real = frame_count / elapsed_time
                print(f"已处理 {frame_count} 帧，FPS: {fps_real:.2f}")
        
        # 释放资源
        cap.release()
        if video_writer is not None:
            video_writer.release()
        
        if show_result:
            cv2.destroyAllWindows()
        
        print(f"视频处理完成，共处理 {frame_count} 帧")
    
    except Exception as e:
        print(f"处理视频过程中出错: {str(e)}")

def main():
    """主函数"""
    args = parse_args()
    
    # 检查源路径
    if not args.source and not args.source.isdigit():
        print("错误: 请指定图片/视频文件路径或摄像头ID")
        return
    
    # 加载模型
    try:
        print(f"正在加载YOLOv11模型: {args.model}")
        model = YOLO(args.model)
        # 启用CUDA加速
        model.to('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"模型加载成功，设备: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return
    
    # 根据源类型进行处理
    source = args.source
    if source.isdigit() or source.startswith('rtsp://') or source.startswith('http://'):
        # 摄像头或流媒体
        process_video(model, source, args.conf, args.save, args.show)
    elif os.path.isdir(source):
        # 文件夹
        process_folder(model, source, args.conf, args.save, args.show)
    elif os.path.isfile(source):
        # 文件
        if source.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
            # 视频文件
            process_video(model, source, args.conf, args.save, args.show)
        elif source.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
            # 图片文件
            detect_image(model, source, args.conf, args.save, args.show)
        else:
            print(f"错误: 不支持的文件类型 {source}")
    else:
        print(f"错误: 源路径不存在 {source}")

if __name__ == "__main__":
    main()