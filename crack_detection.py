# -*- coding: utf-8 -*-
"""
增强版裂缝检测模块

功能：
1. YOLO分割模型集成 - 高精度裂缝分割
2. 多层次图像预处理 - 灰度化 → Gamma校正 → 中值滤波 → 双边滤波
3. 智能轮廓筛选 - 基于面积和圆形度的双重筛选机制
4. 精确面积计算 - 支持像素面积到实际面积的精确转换
5. 鲁棒性强 - 对不同光照条件和图像质量具有良好的适应性
6. 完整可视化 - 标注裂缝轮廓和面积信息
7. 数值结果输出 - 像素面积、实际面积、裂缝数量
8. CSV报告生成 - 批量处理的详细统计报告
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import csv
from pathlib import Path
from skimage.morphology import skeletonize
from skimage import measure
from sklearn.neighbors import KDTree

# 尝试导入YOLO模型
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("警告: ultralytics未安装，YOLO功能将不可用")

# 导入专业裂缝检测模块
try:
    from pavement_crack_detector import PavementCrackDetector
    PAVEMENT_DETECTOR_AVAILABLE = True
except ImportError:
    PAVEMENT_DETECTOR_AVAILABLE = False
    print("警告: 路面裂缝检测模块导入失败")

try:
    from concrete_crack_detector import ConcreteCrackDetector
    CONCRETE_DETECTOR_AVAILABLE = True
except ImportError:
    CONCRETE_DETECTOR_AVAILABLE = False
    print("警告: 混凝土裂缝检测模块导入失败")

class EnhancedCrackDetector:
    def __init__(self, scale_factor=1.0, unit='mm²', yolo_model_path=None,
                 enable_pavement_detection=False, enable_concrete_detection=False):
        """初始化增强版裂缝检测器
        :param scale_factor: 比例尺（像素/实际单位）
        :param unit: 面积单位，默认为平方毫米
        :param yolo_model_path: YOLO分割模型路径
        :param enable_pavement_detection: 是否启用专业路面裂缝检测
        :param enable_concrete_detection: 是否启用专业混凝土裂缝检测
        """
        self.scale_factor = scale_factor
        self.unit = unit
        self.yolo_model = None
        self.pavement_detector = None
        self.concrete_detector = None

        # 加载YOLO模型
        if yolo_model_path and YOLO_AVAILABLE:
            try:
                self.yolo_model = YOLO(yolo_model_path)
                print(f"成功加载YOLO模型: {yolo_model_path}")
            except Exception as e:
                print(f"YOLO模型加载失败: {e}")

        # 初始化路面裂缝检测器
        if enable_pavement_detection and PAVEMENT_DETECTOR_AVAILABLE:
            try:
                # 转换单位（专业检测器使用长度单位）
                length_unit = unit.replace('²', '') if '²' in unit else 'mm'
                self.pavement_detector = PavementCrackDetector(
                    scale_factor=scale_factor,
                    unit=length_unit
                )
                print(f"成功初始化路面裂缝检测器")
            except Exception as e:
                print(f"路面裂缝检测器初始化失败: {e}")

        # 初始化混凝土裂缝检测器
        if enable_concrete_detection and CONCRETE_DETECTOR_AVAILABLE:
            try:
                # 转换单位（专业检测器使用长度单位）
                length_unit = unit.replace('²', '') if '²' in unit else 'mm'
                self.concrete_detector = ConcreteCrackDetector(
                    scale_factor=scale_factor,
                    unit=length_unit
                )
                print(f"成功初始化混凝土裂缝检测器")
            except Exception as e:
                print(f"混凝土裂缝检测器初始化失败: {e}")

        # 多层次图像预处理参数
        self.preprocessing_params = {
            'gamma': 1.2,              # Gamma校正系数
            'median_kernel': 5,        # 中值滤波核大小
            'bilateral_d': 9,          # 双边滤波邻域直径
            'bilateral_sigma_color': 75,  # 双边滤波颜色空间标准差
            'bilateral_sigma_space': 75,  # 双边滤波坐标空间标准差
            'clahe_clip': 2.0,         # CLAHE裁剪限制
            'clahe_grid': (8, 8)       # CLAHE网格大小
        }

        # 智能轮廓筛选参数
        self.contour_filter_params = {
            'min_area': 50,            # 最小轮廓面积
            'max_area': 50000,         # 最大轮廓面积
            'min_circularity': 0.1,    # 最小圆形度
            'max_circularity': 0.9,    # 最大圆形度
            'min_aspect_ratio': 2.0,   # 最小长宽比（裂缝通常是细长的）
        }

        # 检测参数
        self.detection_params = {
            'canny_low': 50,
            'canny_high': 150,
            'morph_kernel_size': 3,
            'adaptive_thresh_block_size': 11,
            'adaptive_thresh_c': 2
        }

    def multilevel_preprocess(self, image):
        """多层次图像预处理
        实现：灰度化 → Gamma校正 → 中值滤波 → 双边滤波
        :param image: 输入图像（BGR格式）
        :return: 预处理后的灰度图像和处理步骤图像字典
        """
        process_steps = {}

        # 步骤1: 灰度化
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        process_steps['1_grayscale'] = gray.copy()

        # 步骤2: Gamma校正增强对比度
        gamma = self.preprocessing_params['gamma']
        gamma_corrected = np.power(gray / 255.0, gamma) * 255.0
        gamma_corrected = gamma_corrected.astype(np.uint8)
        process_steps['2_gamma_corrected'] = gamma_corrected.copy()

        # 步骤3: 中值滤波去噪
        median_filtered = cv2.medianBlur(
            gamma_corrected,
            self.preprocessing_params['median_kernel']
        )
        process_steps['3_median_filtered'] = median_filtered.copy()

        # 步骤4: 双边滤波保留边缘细节
        bilateral_filtered = cv2.bilateralFilter(
            median_filtered,
            self.preprocessing_params['bilateral_d'],
            self.preprocessing_params['bilateral_sigma_color'],
            self.preprocessing_params['bilateral_sigma_space']
        )
        process_steps['4_bilateral_filtered'] = bilateral_filtered.copy()

        # 可选: CLAHE增强对比度
        clahe = cv2.createCLAHE(
            clipLimit=self.preprocessing_params['clahe_clip'],
            tileGridSize=self.preprocessing_params['clahe_grid']
        )
        clahe_enhanced = clahe.apply(bilateral_filtered)
        process_steps['5_clahe_enhanced'] = clahe_enhanced.copy()

        return clahe_enhanced, process_steps

    def yolo_segment_cracks(self, image):
        """使用YOLO模型进行裂缝分割
        :param image: 输入图像
        :return: 分割掩码和置信度信息
        """
        if not self.yolo_model:
            return None, None

        try:
            # YOLO预测
            results = self.yolo_model.predict(image, verbose=False)

            if len(results) == 0 or results[0].masks is None:
                return None, None

            # 获取分割掩码
            masks = results[0].masks.data.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy() if results[0].boxes is not None else None

            # 合并所有掩码
            if len(masks) > 0:
                combined_mask = np.zeros(masks[0].shape, dtype=np.uint8)
                for i, mask in enumerate(masks):
                    # 调整掩码尺寸到原图大小
                    mask_resized = cv2.resize(mask, (image.shape[1], image.shape[0]))
                    combined_mask = np.maximum(combined_mask, (mask_resized * 255).astype(np.uint8))

                return combined_mask, confidences

        except Exception as e:
            print(f"YOLO分割失败: {e}")

        return None, None

    def calculate_circularity(self, contour):
        """计算轮廓的圆形度
        圆形度 = 4π * 面积 / 周长²
        :param contour: 轮廓
        :return: 圆形度值 (0-1)
        """
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)

        if perimeter == 0:
            return 0

        circularity = 4 * np.pi * area / (perimeter * perimeter)
        return min(circularity, 1.0)  # 限制在0-1范围内

    def calculate_aspect_ratio(self, contour):
        """计算轮廓的长宽比
        :param contour: 轮廓
        :return: 长宽比
        """
        rect = cv2.minAreaRect(contour)
        width, height = rect[1]

        if min(width, height) == 0:
            return float('inf')

        aspect_ratio = max(width, height) / min(width, height)
        return aspect_ratio

    def intelligent_contour_filter(self, contours):
        """智能轮廓筛选
        基于面积和圆形度的双重筛选机制
        :param contours: 输入轮廓列表
        :return: 筛选后的轮廓列表和筛选信息
        """
        filtered_contours = []
        filter_info = []

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            circularity = self.calculate_circularity(contour)
            aspect_ratio = self.calculate_aspect_ratio(contour)

            # 面积筛选
            area_valid = (self.contour_filter_params['min_area'] <= area <=
                         self.contour_filter_params['max_area'])

            # 圆形度筛选（裂缝应该不是圆形）
            circularity_valid = (self.contour_filter_params['min_circularity'] <= circularity <=
                               self.contour_filter_params['max_circularity'])

            # 长宽比筛选（裂缝通常是细长的）
            aspect_ratio_valid = aspect_ratio >= self.contour_filter_params['min_aspect_ratio']

            # 综合判断
            is_valid = area_valid and circularity_valid and aspect_ratio_valid

            filter_info.append({
                'index': i,
                'area': area,
                'circularity': circularity,
                'aspect_ratio': aspect_ratio,
                'area_valid': area_valid,
                'circularity_valid': circularity_valid,
                'aspect_ratio_valid': aspect_ratio_valid,
                'is_valid': is_valid
            })

            if is_valid:
                filtered_contours.append(contour)

        return filtered_contours, filter_info

    def detect_pavement_cracks_professional(self, image, return_debug_images=False):
        """专业路面裂缝检测方法
        使用Canny边缘检测 + Gabor滤波器的专业检测流程
        :param image: 输入图像
        :param return_debug_images: 是否返回调试图像
        :return: 检测结果
        """
        if not self.pavement_detector:
            return None, None, {"error": "路面裂缝检测器未初始化"}

        try:
            # 使用专业路面检测器
            pavement_result = self.pavement_detector.detect_pavement_cracks(
                image, return_debug=return_debug_images
            )

            if 'error' in pavement_result:
                return None, None, pavement_result

            # 转换结果格式以兼容原有接口
            contours = pavement_result.get('contours', [])

            # 创建结果图像
            result_image = image.copy()
            if contours:
                cv2.drawContours(result_image, contours, -1, (0, 255, 0), 2)

                # 添加专业测量信息
                y_offset = 30
                font = cv2.FONT_HERSHEY_SIMPLEX

                cv2.putText(result_image, f"专业路面检测", (10, y_offset), font, 0.6, (255, 0, 0), 2)
                y_offset += 25

                cv2.putText(result_image, f"裂缝数量: {pavement_result['num_cracks']}",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"长度: {pavement_result['length_actual']:.2f}{self.pavement_detector.unit}",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"宽度: {pavement_result['width_actual']:.2f}{self.pavement_detector.unit}",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"面积: {pavement_result['area_actual']:.2f}{self.pavement_detector.unit}²",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)

            debug_images = {}
            if return_debug_images and 'debug_info' in pavement_result:
                debug_images = pavement_result['debug_info']
                debug_images['pavement_result'] = result_image

            return contours, result_image, debug_images, pavement_result

        except Exception as e:
            error_result = {"error": f"专业路面检测失败: {str(e)}"}
            return None, None, error_result

    def detect_concrete_cracks_professional(self, image, return_debug_images=False):
        """专业混凝土裂缝检测方法
        使用Canny边缘检测 + Gabor滤波器的专业检测流程
        包含完整的长度、宽度、面积计算
        :param image: 输入图像
        :param return_debug_images: 是否返回调试图像
        :return: 检测结果
        """
        if not self.concrete_detector:
            return None, None, {"error": "混凝土裂缝检测器未初始化"}

        try:
            # 使用专业混凝土检测器
            concrete_result = self.concrete_detector.detect_concrete_cracks(
                image, return_debug=return_debug_images
            )

            if 'error' in concrete_result:
                return None, None, concrete_result

            # 转换结果格式以兼容原有接口
            contours = concrete_result.get('contours', [])

            # 创建结果图像
            result_image = image.copy()
            if contours:
                cv2.drawContours(result_image, contours, -1, (0, 255, 0), 2)

                # 绘制骨架
                if 'skeleton' in concrete_result:
                    skeleton_color = cv2.cvtColor(
                        concrete_result['skeleton'].astype(np.uint8) * 255,
                        cv2.COLOR_GRAY2BGR
                    )
                    skeleton_color[:, :, 0] = 0  # 移除蓝色通道
                    skeleton_color[:, :, 1] = 0  # 移除绿色通道
                    result_image = cv2.addWeighted(result_image, 0.8, skeleton_color, 0.2, 0)

                # 绘制宽度测量线
                if 'width_lines' in concrete_result:
                    for line in concrete_result['width_lines']:
                        if len(line) == 2:
                            pt1, pt2 = line
                            cv2.line(result_image, tuple(map(int, pt1)), tuple(map(int, pt2)), (255, 0, 255), 1)

                # 添加专业测量信息
                y_offset = 30
                font = cv2.FONT_HERSHEY_SIMPLEX

                cv2.putText(result_image, f"专业混凝土检测", (10, y_offset), font, 0.6, (255, 0, 0), 2)
                y_offset += 25

                cv2.putText(result_image, f"裂缝数量: {concrete_result['num_cracks']}",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"长度: {concrete_result['length_actual']:.2f}{self.concrete_detector.unit}",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"平均宽度: {concrete_result['avg_width_actual']:.2f}{self.concrete_detector.unit}",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"面积: {concrete_result['area_actual']:.2f}{self.concrete_detector.unit}²",
                           (10, y_offset), font, 0.6, (0, 0, 255), 2)
                y_offset += 25

                cv2.putText(result_image, f"最大宽度: {concrete_result['max_width_actual']:.2f}{self.concrete_detector.unit}",
                           (10, y_offset), font, 0.5, (128, 128, 128), 1)
                y_offset += 20

                cv2.putText(result_image, f"填充率: {concrete_result['fill_ratio']:.3f}",
                           (10, y_offset), font, 0.5, (128, 128, 128), 1)

            debug_images = {}
            if return_debug_images and 'debug_info' in concrete_result:
                debug_images = concrete_result['debug_info']
                debug_images['concrete_result'] = result_image

            return contours, result_image, debug_images, concrete_result

        except Exception as e:
            error_result = {"error": f"专业混凝土检测失败: {str(e)}"}
            return None, None, error_result

    def detect_cracks(self, image, return_debug_images=False, use_yolo=True,
                     use_pavement_detection=False, use_concrete_detection=False):
        """增强版裂缝检测
        结合YOLO分割、传统图像处理、专业路面检测和专业混凝土检测方法
        :param image: 输入图像（BGR格式）
        :param return_debug_images: 是否返回调试图像
        :param use_yolo: 是否优先使用YOLO分割
        :param use_pavement_detection: 是否使用专业路面检测
        :param use_concrete_detection: 是否使用专业混凝土检测
        :return: 裂缝轮廓列表、标注后的图像、可选调试图像字典
        """
        debug_images = {}
        original_image = image.copy()

        # 方法0a: 专业混凝土裂缝检测（最高优先级）
        if use_concrete_detection and self.concrete_detector:
            contours, result_image, debug_info, concrete_result = self.detect_concrete_cracks_professional(
                image, return_debug_images
            )

            if contours is not None:
                # 专业混凝土检测成功，直接返回结果
                if return_debug_images:
                    debug_images.update(debug_info)
                    return contours, result_image, debug_images, {'concrete_result': concrete_result}
                else:
                    return contours, result_image

        # 方法0b: 专业路面裂缝检测（第二优先级）
        if use_pavement_detection and self.pavement_detector:
            contours, result_image, debug_info, pavement_result = self.detect_pavement_cracks_professional(
                image, return_debug_images
            )

            if contours is not None:
                # 专业路面检测成功，直接返回结果
                if return_debug_images:
                    debug_images.update(debug_info)
                    return contours, result_image, debug_images, {'pavement_result': pavement_result}
                else:
                    return contours, result_image

        # 方法1: 尝试YOLO分割
        yolo_mask = None
        if use_yolo and self.yolo_model:
            yolo_mask, confidences = self.yolo_segment_cracks(image)
            if yolo_mask is not None:
                debug_images['yolo_mask'] = yolo_mask

        # 方法2: 多层次图像预处理
        processed_image, process_steps = self.multilevel_preprocess(image)
        debug_images.update(process_steps)

        # 方法3: 传统图像处理检测
        # 自适应阈值处理
        binary = cv2.adaptiveThreshold(
            processed_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV,
            self.detection_params['adaptive_thresh_block_size'],
            self.detection_params['adaptive_thresh_c']
        )
        debug_images['binary'] = binary

        # Canny边缘检测
        edges = cv2.Canny(
            processed_image,
            self.detection_params['canny_low'],
            self.detection_params['canny_high']
        )
        debug_images['edges'] = edges

        # 形态学处理
        kernel_size = self.detection_params['morph_kernel_size']
        kernel = np.ones((kernel_size, kernel_size), np.uint8)

        # 闭运算连接断裂的边缘
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        debug_images['closed'] = closed

        # 开运算去除噪声
        opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel)
        debug_images['opened'] = opened

        # 结合YOLO结果（如果可用）
        final_mask = opened
        if yolo_mask is not None:
            # 将YOLO结果与传统方法结合
            combined = cv2.bitwise_or(opened, yolo_mask)
            final_mask = combined
            debug_images['combined_mask'] = combined

        # 提取轮廓
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 智能轮廓筛选
        filtered_contours, filter_info = self.intelligent_contour_filter(contours)

        # 标注结果
        result = original_image.copy()
        if len(filtered_contours) > 0:
            cv2.drawContours(result, filtered_contours, -1, (0, 255, 0), 2)

        debug_images['final_result'] = result

        if return_debug_images:
            return filtered_contours, result, debug_images, filter_info

        return filtered_contours, result

    def calculate_precise_area(self, contours, decimal_places=2):
        """精确计算裂缝面积
        支持像素面积到实际面积的精确转换
        :param contours: 裂缝轮廓列表
        :param decimal_places: 小数点位数
        :return: 详细的面积计算结果
        """
        if not contours:
            return {
                'total_pixel_area': 0,
                'total_real_area': 0,
                'contour_count': 0,
                'individual_pixel_areas': [],
                'individual_real_areas': [],
                'area_statistics': {
                    'mean_pixel_area': 0,
                    'mean_real_area': 0,
                    'max_pixel_area': 0,
                    'max_real_area': 0,
                    'min_pixel_area': 0,
                    'min_real_area': 0,
                    'std_pixel_area': 0,
                    'std_real_area': 0
                }
            }

        # 计算每个轮廓的像素面积
        pixel_areas = [cv2.contourArea(cnt) for cnt in contours]

        # 转换为实际面积
        real_areas = [area * (self.scale_factor ** 2) for area in pixel_areas]

        # 格式化小数位数
        real_areas = [round(area, decimal_places) for area in real_areas]

        # 总面积
        total_pixel_area = sum(pixel_areas)
        total_real_area = round(sum(real_areas), decimal_places)

        # 统计信息
        area_stats = {
            'mean_pixel_area': round(np.mean(pixel_areas), decimal_places),
            'mean_real_area': round(np.mean(real_areas), decimal_places),
            'max_pixel_area': max(pixel_areas),
            'max_real_area': max(real_areas),
            'min_pixel_area': min(pixel_areas),
            'min_real_area': min(real_areas),
            'std_pixel_area': round(np.std(pixel_areas), decimal_places),
            'std_real_area': round(np.std(real_areas), decimal_places)
        }

        return {
            'total_pixel_area': total_pixel_area,
            'total_real_area': total_real_area,
            'contour_count': len(contours),
            'individual_pixel_areas': pixel_areas,
            'individual_real_areas': real_areas,
            'area_statistics': area_stats
        }

    def generate_csv_report(self, results_list, output_path):
        """生成CSV报告
        批量处理的详细统计报告
        :param results_list: 处理结果列表
        :param output_path: CSV文件输出路径
        """
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    '图像路径', '处理时间', '裂缝数量', '总像素面积', '总实际面积',
                    '平均像素面积', '平均实际面积', '最大像素面积', '最大实际面积',
                    '最小像素面积', '最小实际面积', '面积标准差(像素)', '面积标准差(实际)',
                    '比例尺', '面积单位', '检测方法', '处理状态'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for result in results_list:
                    if 'error' in result:
                        writer.writerow({
                            '图像路径': result.get('image_path', ''),
                            '处理时间': result.get('timestamp', ''),
                            '处理状态': f"错误: {result['error']}"
                        })
                        continue

                    area_info = result.get('area_info', {})
                    stats = area_info.get('area_statistics', {})

                    writer.writerow({
                        '图像路径': result.get('image_path', ''),
                        '处理时间': result.get('timestamp', ''),
                        '裂缝数量': area_info.get('contour_count', 0),
                        '总像素面积': area_info.get('total_pixel_area', 0),
                        '总实际面积': area_info.get('total_real_area', 0),
                        '平均像素面积': stats.get('mean_pixel_area', 0),
                        '平均实际面积': stats.get('mean_real_area', 0),
                        '最大像素面积': stats.get('max_pixel_area', 0),
                        '最大实际面积': stats.get('max_real_area', 0),
                        '最小像素面积': stats.get('min_pixel_area', 0),
                        '最小实际面积': stats.get('min_real_area', 0),
                        '面积标准差(像素)': stats.get('std_pixel_area', 0),
                        '面积标准差(实际)': stats.get('std_real_area', 0),
                        '比例尺': self.scale_factor,
                        '面积单位': self.unit,
                        '检测方法': result.get('detection_method', 'Enhanced'),
                        '处理状态': '成功'
                    })

            print(f"CSV报告已生成: {output_path}")
            return True

        except Exception as e:
            print(f"CSV报告生成失败: {e}")
            return False

    def enhanced_visualize_results(self, image, contours, area_info, save_path=None,
                                 show_individual_labels=True, show_statistics=True):
        """增强版结果可视化
        标注裂缝轮廓和面积信息
        :param image: 原始图像
        :param contours: 裂缝轮廓列表
        :param area_info: 面积计算结果
        :param save_path: 保存路径
        :param show_individual_labels: 是否显示单个裂缝标签
        :param show_statistics: 是否显示统计信息
        :return: 标注后的图像
        """
        result = image.copy()

        if not contours:
            # 如果没有检测到裂缝
            cv2.putText(result, "未检测到裂缝", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
            if save_path:
                os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
                cv2.imwrite(save_path, result)
            return result

        # 绘制轮廓 - 使用不同颜色
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
        for i, contour in enumerate(contours):
            color = colors[i % len(colors)]
            cv2.drawContours(result, [contour], -1, color, 2)

        # 添加统计信息
        if show_statistics:
            y_offset = 30
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            thickness = 2

            # 基本信息
            cv2.putText(result, f"裂缝数量: {area_info['contour_count']}",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 30

            cv2.putText(result, f"总面积: {area_info['total_real_area']:.2f}{self.unit}",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 30

            cv2.putText(result, f"平均面积: {area_info['area_statistics']['mean_real_area']:.2f}{self.unit}",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 30

            cv2.putText(result, f"比例尺: 1像素 = {self.scale_factor:.3f}{self.unit.replace('²', '')}",
                       (10, y_offset), font, 0.5, (128, 128, 128), 1)

        # 标记单个裂缝
        if show_individual_labels:
            for i, (contour, real_area) in enumerate(zip(contours, area_info['individual_real_areas'])):
                # 计算轮廓中心
                M = cv2.moments(contour)
                if M["m00"] > 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])

                    # 标注编号和面积
                    label = f"#{i+1}: {real_area:.2f}{self.unit}"
                    cv2.putText(result, label, (cx-20, cy),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                    cv2.putText(result, label, (cx-20, cy),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

                    # 标记中心点
                    cv2.circle(result, (cx, cy), 3, (255, 255, 255), -1)
                    cv2.circle(result, (cx, cy), 2, (0, 0, 0), -1)

        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(result, timestamp, (10, result.shape[0] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)

        # 保存结果
        if save_path:
            save_dir = os.path.dirname(save_path)
            if save_dir:
                os.makedirs(save_dir, exist_ok=True)
            cv2.imwrite(save_path, result)

        return result

    def process_single_image(self, image_path, output_dir=None, use_yolo=True,
                           use_pavement_detection=False, use_concrete_detection=False):
        """处理单张图像的完整流程
        :param image_path: 图像路径
        :param output_dir: 输出目录，如果提供则保存结果
        :param use_yolo: 是否使用YOLO分割
        :param use_pavement_detection: 是否使用专业路面检测
        :param use_concrete_detection: 是否使用专业混凝土检测
        :return: 处理结果字典
        """
        start_time = datetime.now()

        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return {
                    "error": f"无法读取图像: {image_path}",
                    "image_path": image_path,
                    "timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S")
                }

            # 检测裂缝
            contours, result_image, debug_images, filter_info = self.detect_cracks(
                image, return_debug_images=True, use_yolo=use_yolo,
                use_pavement_detection=use_pavement_detection,
                use_concrete_detection=use_concrete_detection
            )

            # 精确计算面积
            area_info = self.calculate_precise_area(contours)

            # 准备输出路径
            save_path = None
            debug_save_dir = None
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                filename = os.path.basename(image_path)
                base_name = os.path.splitext(filename)[0]
                timestamp = start_time.strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join(output_dir, f"{base_name}_enhanced_result_{timestamp}.jpg")

                # 创建调试图像保存目录
                debug_save_dir = os.path.join(output_dir, f"{base_name}_debug_{timestamp}")
                os.makedirs(debug_save_dir, exist_ok=True)

            # 增强版可视化结果
            result = self.enhanced_visualize_results(image, contours, area_info, save_path)

            # 保存调试图像
            if debug_save_dir:
                for name, img in debug_images.items():
                    debug_path = os.path.join(debug_save_dir, f"{name}.jpg")
                    cv2.imwrite(debug_path, img)

            # 处理时间
            processing_time = (datetime.now() - start_time).total_seconds()

            # 检测方法标识
            detection_method = "Enhanced"
            if use_yolo and self.yolo_model:
                detection_method += "+YOLO"

            # 返回结果
            return {
                "image_path": image_path,
                "timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "processing_time": processing_time,
                "contours": contours,
                "area_info": area_info,
                "filter_info": filter_info,
                "result_image": result,
                "debug_images": debug_images,
                "save_path": save_path,
                "debug_save_dir": debug_save_dir,
                "detection_method": detection_method,
                "scale_factor": self.scale_factor,
                "unit": self.unit
            }

        except Exception as e:
            return {
                "error": f"处理过程中出错: {str(e)}",
                "image_path": image_path,
                "timestamp": start_time.strftime("%Y-%m-%d %H:%M:%S")
            }

    def batch_process_images(self, image_paths, output_dir, use_yolo=True,
                           use_pavement_detection=False, use_concrete_detection=False,
                           generate_csv=True, progress_callback=None):
        """批量处理图像
        :param image_paths: 图像路径列表或包含图像的文件夹路径
        :param output_dir: 输出目录
        :param use_yolo: 是否使用YOLO分割
        :param use_pavement_detection: 是否使用专业路面检测
        :param use_concrete_detection: 是否使用专业混凝土检测
        :param generate_csv: 是否生成CSV报告
        :param progress_callback: 进度回调函数
        :return: 处理结果列表
        """
        # 处理输入路径
        if isinstance(image_paths, str):
            # 如果是文件夹路径，获取所有图像文件
            folder_path = Path(image_paths)
            if folder_path.is_dir():
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
                image_paths = []
                for ext in image_extensions:
                    image_paths.extend(list(folder_path.glob(f'*{ext}')))
                    image_paths.extend(list(folder_path.glob(f'*{ext.upper()}')))
                image_paths = [str(p) for p in image_paths]
            else:
                image_paths = [image_paths]

        if not image_paths:
            print("未找到图像文件")
            return []

        print(f"开始批量处理 {len(image_paths)} 张图像...")

        results = []
        successful_count = 0
        failed_count = 0

        for i, image_path in enumerate(image_paths):
            print(f"处理图像 {i+1}/{len(image_paths)}: {os.path.basename(image_path)}")

            # 处理单张图像
            result = self.process_single_image(
                image_path, output_dir, use_yolo,
                use_pavement_detection, use_concrete_detection
            )
            results.append(result)

            # 统计结果
            if 'error' in result:
                failed_count += 1
                print(f"  失败: {result['error']}")
            else:
                successful_count += 1
                area_info = result['area_info']
                print(f"  成功: 检测到 {area_info['contour_count']} 个裂缝, "
                      f"总面积 {area_info['total_real_area']:.2f}{self.unit}")

            # 调用进度回调
            if progress_callback:
                progress = int(((i + 1) / len(image_paths)) * 100)
                progress_callback(progress)

        # 生成CSV报告
        if generate_csv and output_dir:
            csv_path = os.path.join(output_dir, f"crack_detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            self.generate_csv_report(results, csv_path)

        # 打印总结
        print(f"\n批量处理完成:")
        print(f"  成功: {successful_count} 张")
        print(f"  失败: {failed_count} 张")
        print(f"  总计: {len(image_paths)} 张")

        return results

    def set_scale_factor(self, scale_factor):
        """设置比例尺
        :param scale_factor: 比例尺（像素/实际单位）
        """
        self.scale_factor = scale_factor
        print(f"比例尺已设置为: 1像素 = {scale_factor:.3f}{self.unit.replace('²', '')}")

    def set_unit(self, unit):
        """设置面积单位
        :param unit: 面积单位
        """
        self.unit = unit
        print(f"面积单位已设置为: {unit}")

    def update_preprocessing_params(self, **kwargs):
        """更新预处理参数
        :param kwargs: 预处理参数字典
        """
        for key, value in kwargs.items():
            if key in self.preprocessing_params:
                self.preprocessing_params[key] = value
                print(f"预处理参数 {key} 已更新为: {value}")
            else:
                print(f"警告: 未知的预处理参数 {key}")

    def update_contour_filter_params(self, **kwargs):
        """更新轮廓筛选参数
        :param kwargs: 轮廓筛选参数字典
        """
        for key, value in kwargs.items():
            if key in self.contour_filter_params:
                self.contour_filter_params[key] = value
                print(f"轮廓筛选参数 {key} 已更新为: {value}")
            else:
                print(f"警告: 未知的轮廓筛选参数 {key}")

    def update_detection_params(self, **kwargs):
        """更新检测参数
        :param kwargs: 检测参数字典
        """
        for key, value in kwargs.items():
            if key in self.detection_params:
                self.detection_params[key] = value
                print(f"检测参数 {key} 已更新为: {value}")
            else:
                print(f"警告: 未知的检测参数 {key}")

    def get_current_params(self):
        """获取当前所有参数
        :return: 参数字典
        """
        return {
            'scale_factor': self.scale_factor,
            'unit': self.unit,
            'preprocessing_params': self.preprocessing_params.copy(),
            'contour_filter_params': self.contour_filter_params.copy(),
            'detection_params': self.detection_params.copy(),
            'yolo_available': self.yolo_model is not None
        }

    def print_params(self):
        """打印当前参数设置"""
        print("\n=== 当前参数设置 ===")
        print(f"比例尺: 1像素 = {self.scale_factor:.3f}{self.unit.replace('²', '')}")
        print(f"面积单位: {self.unit}")
        print(f"YOLO模型: {'已加载' if self.yolo_model else '未加载'}")

        print("\n预处理参数:")
        for key, value in self.preprocessing_params.items():
            print(f"  {key}: {value}")

        print("\n轮廓筛选参数:")
        for key, value in self.contour_filter_params.items():
            print(f"  {key}: {value}")

        print("\n检测参数:")
        for key, value in self.detection_params.items():
            print(f"  {key}: {value}")
        print("=" * 30)

# 兼容性类（保持向后兼容）
class CrackDetector(EnhancedCrackDetector):
    """向后兼容的裂缝检测器类"""
    def __init__(self, scale_factor=1.0, unit='mm²'):
        super().__init__(scale_factor, unit)

    def preprocess_image(self, image, advanced=True):
        """兼容旧版本的预处理方法"""
        processed, _ = self.multilevel_preprocess(image)
        return processed

    def calculate_area(self, contours, decimal_places=2):
        """兼容旧版本的面积计算方法"""
        area_info = self.calculate_precise_area(contours, decimal_places)
        return (area_info['total_pixel_area'],
                area_info['total_real_area'],
                area_info['individual_real_areas'])

    def visualize_results(self, image, contours, pixel_area, real_area, save_path=None):
        """兼容旧版本的可视化方法"""
        area_info = {
            'total_pixel_area': pixel_area,
            'total_real_area': real_area,
            'contour_count': len(contours),
            'individual_real_areas': [cv2.contourArea(cnt) * (self.scale_factor ** 2) for cnt in contours],
            'area_statistics': {'mean_real_area': real_area / len(contours) if contours else 0}
        }
        return self.enhanced_visualize_results(image, contours, area_info, save_path,
                                             show_individual_labels=False, show_statistics=True)

    def process_image(self, image_path, output_dir=None):
        """兼容旧版本的图像处理方法"""
        result = self.process_single_image(image_path, output_dir, use_yolo=False)
        if 'error' in result:
            return result

        # 转换为旧格式
        area_info = result['area_info']
        return {
            "image_path": result['image_path'],
            "contours": result['contours'],
            "contour_count": area_info['contour_count'],
            "pixel_area": area_info['total_pixel_area'],
            "real_area": area_info['total_real_area'],
            "contour_areas": area_info['individual_real_areas'],
            "result_image": result['result_image'],
            "debug_images": result['debug_images'],
            "save_path": result['save_path']
        }

    def batch_process(self, image_paths, output_dir):
        """兼容旧版本的批量处理方法"""
        results = self.batch_process_images(image_paths, output_dir, use_yolo=False, generate_csv=False)
        # 转换为旧格式
        converted_results = []
        for result in results:
            if 'error' in result:
                converted_results.append(result)
            else:
                converted_results.append(self.process_image(result['image_path'], output_dir))
        return converted_results

# 示例用法
if __name__ == "__main__":
    print("=== 增强版裂缝检测系统示例 ===\n")

    # 创建检测器实例
    detector = EnhancedCrackDetector(
        scale_factor=0.1,  # 1像素 = 0.1mm
        unit='mm²',
        yolo_model_path="yolo11n-seg.pt"  # 可选：YOLO模型路径
    )

    # 打印当前参数
    detector.print_params()

    # 示例1: 单张图像检测
    print("\n=== 示例1: 单张图像检测 ===")
    test_image_path = "test_images/test_crack.jpg"

    if os.path.exists(test_image_path):
        result = detector.process_single_image(
            test_image_path,
            output_dir="output/single_test",
            use_yolo=True
        )

        if 'error' not in result:
            area_info = result['area_info']
            print(f"检测结果:")
            print(f"  裂缝数量: {area_info['contour_count']}")
            print(f"  总面积: {area_info['total_real_area']:.2f}{detector.unit}")
            print(f"  处理时间: {result['processing_time']:.2f}秒")
            print(f"  结果保存至: {result['save_path']}")
        else:
            print(f"检测失败: {result['error']}")
    else:
        print(f"测试图像不存在: {test_image_path}")

    # 示例2: 批量处理
    print("\n=== 示例2: 批量处理 ===")
    test_folder = "test_images"

    if os.path.exists(test_folder):
        results = detector.batch_process_images(
            test_folder,
            output_dir="output/batch_test",
            use_yolo=True,
            generate_csv=True
        )

        print(f"批量处理完成，共处理 {len(results)} 张图像")
    else:
        print(f"测试文件夹不存在: {test_folder}")

    # 示例3: 参数调整
    print("\n=== 示例3: 参数调整 ===")
    detector.set_scale_factor(0.05)  # 调整比例尺
    detector.update_contour_filter_params(min_area=30, max_area=10000)  # 调整筛选参数
    detector.update_detection_params(canny_low=30, canny_high=100)  # 调整检测参数

    print("\n参数调整完成！")

    print("\n=== 示例完成 ===")
    print("更多功能请参考文档或查看源代码注释")