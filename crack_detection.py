# -*- coding: utf-8 -*-
"""
裂缝检测模块

功能：
1. 图像预处理（灰度化、对比度增强、去噪）
2. 裂缝检测（边缘检测、形态学处理、轮廓提取）
3. 面积计算（像素面积和实际面积转换）
4. 结果可视化（标注裂缝区域和面积信息）
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

class CrackDetector:
    def __init__(self, scale_factor=1.0, unit='mm²'):
        """初始化裂缝检测器
        :param scale_factor: 比例尺（像素/实际单位）
        :param unit: 面积单位，默认为平方毫米
        """
        self.scale_factor = scale_factor
        self.unit = unit
        self.preprocessing_params = {
            'gamma': 1.5,
            'blur_kernel': 5,
            'clahe_clip': 2.0,
            'clahe_grid': (8, 8)
        }
        self.detection_params = {
            'canny_low': 50,
            'canny_high': 150,
            'morph_kernel_size': 5,
            'min_area': 100
        }

    def preprocess_image(self, image, advanced=True):
        """图像预处理
        :param image: 输入图像（BGR格式）
        :param advanced: 是否使用高级预处理
        :return: 预处理后的灰度图像
        """
        # 灰度化
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        if advanced:
            # 自适应直方图均衡化(CLAHE)增强对比度
            clahe = cv2.createCLAHE(
                clipLimit=self.preprocessing_params['clahe_clip'], 
                tileGridSize=self.preprocessing_params['clahe_grid']
            )
            gray = clahe.apply(gray)
            
        # Gamma校正增强对比度
        gamma = self.preprocessing_params['gamma']
        gray = np.power(gray / 255.0, gamma) * 255.0
        gray = gray.astype(np.uint8)
        
        # 中值滤波去噪
        gray = cv2.medianBlur(gray, self.preprocessing_params['blur_kernel'])
        
        # 双边滤波保留边缘细节
        gray = cv2.bilateralFilter(gray, 9, 75, 75)
        
        return gray

    def detect_cracks(self, image, return_debug_images=False):
        """检测裂缝
        :param image: 输入图像（BGR格式）
        :param return_debug_images: 是否返回调试图像
        :return: 裂缝轮廓列表和标注后的图像，可选返回调试图像字典
        """
        # 预处理
        gray = self.preprocess_image(image)
        
        # 自适应阈值处理
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # Canny边缘检测
        edges = cv2.Canny(
            gray, 
            self.detection_params['canny_low'], 
            self.detection_params['canny_high']
        )
        
        # 形态学闭运算连接边缘
        kernel_size = self.detection_params['morph_kernel_size']
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # 提取轮廓
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选有效轮廓（面积大于阈值）
        min_area = self.detection_params['min_area']
        valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
        
        # 标注轮廓
        result = image.copy()
        cv2.drawContours(result, valid_contours, -1, (0, 255, 0), 2)
        
        if return_debug_images:
            debug_images = {
                'gray': gray,
                'binary': binary,
                'edges': edges,
                'closed': closed,
                'result': result
            }
            return valid_contours, result, debug_images
        
        return valid_contours, result

    def calculate_area(self, contours, decimal_places=2):
        """计算裂缝面积
        :param contours: 裂缝轮廓列表
        :param decimal_places: 小数点位数
        :return: 像素面积和实际面积，以及各个轮廓的面积列表
        """
        # 计算每个轮廓的面积
        contour_areas = [cv2.contourArea(cnt) for cnt in contours]
        
        # 总像素面积
        pixel_area = sum(contour_areas)
        
        # 转换为实际面积
        real_area = pixel_area * (self.scale_factor ** 2)
        
        # 格式化为指定小数位
        real_area = round(real_area, decimal_places)
        
        # 各轮廓实际面积
        real_contour_areas = [round(area * (self.scale_factor ** 2), decimal_places) for area in contour_areas]
        
        return pixel_area, real_area, real_contour_areas

    def visualize_results(self, image, contours, pixel_area, real_area, save_path=None):
    """仅生成结果图像，不直接显示"""
    result = image.copy()
    # 绘制轮廓
    cv2.drawContours(result, contours, -1, (0, 255, 0), 2)
    
    # 添加文本信息
    cv2.putText(result, f"裂缝数量: {len(contours)}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.putText(result, f"总面积: {real_area:.2f}{self.unit}", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    
    # 标记单个裂缝
    for i, cnt in enumerate(contours):
        M = cv2.moments(cnt)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            area = cv2.contourArea(cnt) * (self.scale_factor ** 2)
            cv2.putText(result, f"#{i+1}:{area:.2f}{self.unit}", (cx, cy),
                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
    
    # 保存结果
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        cv2.imwrite(save_path, result)
    
    return result  # 返回图像数据供主线程显示

    def process_image(self, image_path, output_dir=None):
        """处理单张图像的完整流程
        :param image_path: 图像路径
        :param output_dir: 输出目录，如果提供则保存结果
        :return: 处理结果字典
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            return {"error": f"无法读取图像: {image_path}"}
        
        # 检测裂缝
        contours, result_image, debug_images = self.detect_cracks(image, return_debug_images=True)
        
        # 计算面积
        pixel_area, real_area, contour_areas = self.calculate_area(contours)
        
        # 准备输出路径
        save_path = None
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            filename = os.path.basename(image_path)
            base_name = os.path.splitext(filename)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(output_dir, f"{base_name}_result_{timestamp}.jpg")
        
        # 可视化结果
        result = self.visualize_results(image, contours, pixel_area, real_area, save_path)
        
        # 返回结果
        return {
            "image_path": image_path,
            "contours": contours,
            "contour_count": len(contours),
            "pixel_area": pixel_area,
            "real_area": real_area,
            "contour_areas": contour_areas,
            "result_image": result,
            "debug_images": debug_images,
            "save_path": save_path
        }

    def batch_process(self, image_paths, output_dir):
        """批量处理图像
        :param image_paths: 图像路径列表
        :param output_dir: 输出目录
        :return: 处理结果列表
        """
        results = []
        for image_path in image_paths:
            result = self.process_image(image_path, output_dir)
            results.append(result)
        return results

    def set_scale_factor(self, scale_factor):
        """设置比例尺
        :param scale_factor: 比例尺（像素/实际单位）
        """
        self.scale_factor = scale_factor
        
    def set_unit(self, unit):
        """设置面积单位
        :param unit: 面积单位
        """
        self.unit = unit
        
    def adjust_detection_params(self, canny_low=None, canny_high=None, 
                               morph_kernel_size=None, min_area=None):
        """调整检测参数
        :param canny_low: Canny边缘检测低阈值
        :param canny_high: Canny边缘检测高阈值
        :param morph_kernel_size: 形态学操作核大小
        :param min_area: 最小轮廓面积
        """
        if canny_low is not None:
            self.detection_params['canny_low'] = canny_low
        if canny_high is not None:
            self.detection_params['canny_high'] = canny_high
        if morph_kernel_size is not None:
            self.detection_params['morph_kernel_size'] = morph_kernel_size
        if min_area is not None:
            self.detection_params['min_area'] = min_area

# 示例用法
if __name__ == "__main__":
    detector = CrackDetector(scale_factor=0.1, unit='mm²')
    image = cv2.imread("test_image.jpg")
    if image is not None:
        contours, result = detector.detect_cracks(image)
        pixel_area, real_area, _ = detector.calculate_area(contours)
        detector.visualize_results(result, contours, pixel_area, real_area)
    else:
        print("Error: Could not load image.")