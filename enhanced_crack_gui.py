# -*- coding: utf-8 -*-
"""
增强版裂缝检测GUI界面

功能：
1. 加载图片选择要检测的图像
2. 调整参数（比例尺、最小面积阈值）
3. 点击"开始检测"执行检测
4. 查看结果并保存
5. 实时显示检测结果
6. 参数调节界面
7. 批量处理功能
8. 宽度计算功能
"""

import sys
import os
import cv2
import numpy as np
from datetime import datetime
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFileDialog, QLineEdit, QMessageBox,
    QProgressBar, QCheckBox, QGroupBox, QSlider, QSpinBox,
    QDoubleSpinBox, QTabWidget, QTextEdit, QSplitter, QFrame,
    QGridLayout, QComboBox, QScrollArea
)
from PyQt5.QtGui import QPixmap, QImage, QFont, QIcon
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize

# 设置全局字体为微软雅黑
def setup_font():
    """设置全局字体为微软雅黑"""
    font = QFont("Microsoft YaHei", 9)  # 微软雅黑，9号字体
    QApplication.setFont(font)
    return font

# 导入我们的检测模块
from crack_detection import EnhancedCrackDetector
from crack_width_calculator import CrackWidthCalculator
from advanced_segmentation_system import AdvancedSegmentationSystem
from optimal_crack_analysis_system import OptimalCrackAnalysisSystem, AnalysisMode


class ImageProcessingThread(QThread):
    """图像处理线程"""
    finished = pyqtSignal(dict)
    progress = pyqtSignal(int)
    status_update = pyqtSignal(str)

    def __init__(self, detector, image_path, use_yolo=True,
                 use_pavement_detection=False, use_concrete_detection=False,
                 calculate_width=False, optimal_system=None, analysis_mode=None):
        super().__init__()
        self.detector = detector
        self.optimal_system = optimal_system
        self.image_path = image_path
        self.use_yolo = use_yolo
        self.use_pavement_detection = use_pavement_detection
        self.use_concrete_detection = use_concrete_detection
        self.calculate_width = calculate_width
        self.analysis_mode = analysis_mode
        self.width_calculator = None

        if calculate_width:
            self.width_calculator = CrackWidthCalculator(
                scale_factor=detector.scale_factor,
                unit=detector.unit.replace('²', '')
            )
    
    def run(self):
        try:
            self.status_update.emit("开始处理图像...")
            self.progress.emit(10)

            # 如果有最优系统且指定了分析模式，使用最优系统
            if self.optimal_system and self.analysis_mode:
                self.status_update.emit("使用最优分析系统...")

                # 加载图像
                image = cv2.imread(self.image_path)
                if image is None:
                    raise Exception("无法加载图像")

                # 执行最优分析
                result = self.optimal_system.analyze_image(image, self.analysis_mode)

                # 转换结果格式以兼容现有界面
                if result['success']:
                    area_info = result['area_analysis']
                    result['area_info'] = area_info
                    result['processing_time'] = result['analysis_time']
                    result['detection_method'] = f"最优系统-{result['analysis_mode']}"

                    # 生成可视化结果
                    if 'segmentation_info' in result:
                        # 这里可以添加可视化代码
                        pass

                self.progress.emit(70)
            else:
                # 使用传统检测器
                result = self.detector.process_single_image(
                    self.image_path,
                    output_dir="output/gui_results",
                    use_yolo=self.use_yolo,
                    use_pavement_detection=self.use_pavement_detection,
                    use_concrete_detection=self.use_concrete_detection
                )

                self.progress.emit(70)
            
            # 如果需要计算宽度
            if self.calculate_width and 'error' not in result and self.width_calculator:
                self.status_update.emit("计算裂缝宽度...")
                
                # 从检测结果创建二值掩码
                image = cv2.imread(self.image_path)
                contours = result['contours']
                
                if contours:
                    mask = np.zeros(image.shape[:2], dtype=np.uint8)
                    cv2.fillPoly(mask, contours, 255)
                    
                    width_result = self.width_calculator.calculate_crack_width(mask, return_debug=True)
                    result['width_info'] = width_result
                    
                    # 可视化宽度结果
                    width_vis = self.width_calculator.visualize_width_results(
                        image, width_result, 
                        save_path=f"output/gui_results/width_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                    )
                    result['width_visualization'] = width_vis
            
            self.progress.emit(100)
            self.status_update.emit("处理完成")
            self.finished.emit(result)
            
        except Exception as e:
            error_result = {
                'error': f"处理失败: {str(e)}",
                'image_path': self.image_path
            }
            self.finished.emit(error_result)


class BatchProcessingThread(QThread):
    """批量处理线程"""
    finished = pyqtSignal(list)
    progress = pyqtSignal(int)
    status_update = pyqtSignal(str)
    
    def __init__(self, detector, folder_path, use_yolo=True):
        super().__init__()
        self.detector = detector
        self.folder_path = folder_path
        self.use_yolo = use_yolo
    
    def run(self):
        try:
            def progress_callback(progress):
                self.progress.emit(progress)
                self.status_update.emit(f"批量处理进度: {progress}%")
            
            results = self.detector.batch_process_images(
                self.folder_path,
                output_dir="output/batch_results",
                use_yolo=self.use_yolo,
                generate_csv=True,
                progress_callback=progress_callback
            )
            
            self.finished.emit(results)
            
        except Exception as e:
            self.status_update.emit(f"批量处理失败: {str(e)}")
            self.finished.emit([])


class EnhancedCrackDetectionGUI(QMainWindow):
    """增强版裂缝检测GUI主界面"""
    
    def __init__(self):
        super().__init__()

        # 设置全局字体
        self.font = setup_font()

        self.detector = None
        self.optimal_system = None
        self.current_image_path = None
        self.current_result = None
        self.processing_thread = None
        self.batch_thread = None

        self.init_ui()
        self.init_detector()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("增强版裂缝检测系统 v2.0")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 右侧显示区域
        self.create_display_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 1000])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setMaximumWidth(400)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 1. 图像选择组
        self.create_image_selection_group(scroll_layout)
        
        # 2. 参数设置组
        self.create_parameter_group(scroll_layout)
        
        # 3. 检测选项组
        self.create_detection_options_group(scroll_layout)
        
        # 4. 操作按钮组
        self.create_action_buttons_group(scroll_layout)
        
        # 5. 结果显示组
        self.create_results_display_group(scroll_layout)
        
        scroll_layout.addStretch()
        scroll.setWidget(scroll_content)
        control_layout.addWidget(scroll)
        
        parent.addWidget(control_widget)
    
    def create_image_selection_group(self, layout):
        """创建图像选择组"""
        group = QGroupBox("图像选择")
        group_layout = QVBoxLayout(group)
        
        # 单张图片选择
        single_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("选择要检测的图像文件")
        self.image_path_edit.setReadOnly(True)
        
        browse_btn = QPushButton("浏览图片")
        browse_btn.clicked.connect(self.browse_image)
        
        single_layout.addWidget(self.image_path_edit)
        single_layout.addWidget(browse_btn)
        group_layout.addLayout(single_layout)
        
        # 文件夹选择
        folder_layout = QHBoxLayout()
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setPlaceholderText("选择图片文件夹（批量处理）")
        self.folder_path_edit.setReadOnly(True)
        
        browse_folder_btn = QPushButton("浏览文件夹")
        browse_folder_btn.clicked.connect(self.browse_folder)
        
        folder_layout.addWidget(self.folder_path_edit)
        folder_layout.addWidget(browse_folder_btn)
        group_layout.addLayout(folder_layout)
        
        layout.addWidget(group)
    
    def create_parameter_group(self, layout):
        """创建参数设置组"""
        group = QGroupBox("参数设置")
        group_layout = QGridLayout(group)
        
        # 分析模式选择
        group_layout.addWidget(QLabel("分析模式:"), 0, 0)
        self.analysis_mode_combo = QComboBox()
        self.analysis_mode_combo.addItems(["快速模式", "平衡模式", "精确模式", "研究模式"])
        self.analysis_mode_combo.setCurrentText("平衡模式")
        self.analysis_mode_combo.setToolTip("选择分析精度和速度的平衡")
        group_layout.addWidget(self.analysis_mode_combo, 0, 1)

        # 比例尺设置
        group_layout.addWidget(QLabel("比例尺:"), 1, 0)
        self.scale_factor_spin = QDoubleSpinBox()
        self.scale_factor_spin.setRange(0.001, 10.0)
        self.scale_factor_spin.setValue(0.1)
        self.scale_factor_spin.setDecimals(3)
        self.scale_factor_spin.setSuffix(" mm/像素")
        group_layout.addWidget(self.scale_factor_spin, 1, 1)
        
        # 面积单位
        group_layout.addWidget(QLabel("面积单位:"), 2, 0)
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(["mm²", "cm²", "m²", "像素²"])
        group_layout.addWidget(self.unit_combo, 2, 1)

        # 最小面积阈值
        group_layout.addWidget(QLabel("最小面积:"), 3, 0)
        self.min_area_spin = QSpinBox()
        self.min_area_spin.setRange(10, 10000)
        self.min_area_spin.setValue(50)
        self.min_area_spin.setSuffix(" 像素²")
        group_layout.addWidget(self.min_area_spin, 3, 1)
        
        # 最大面积阈值
        group_layout.addWidget(QLabel("最大面积:"), 3, 0)
        self.max_area_spin = QSpinBox()
        self.max_area_spin.setRange(100, 100000)
        self.max_area_spin.setValue(50000)
        self.max_area_spin.setSuffix(" 像素²")
        group_layout.addWidget(self.max_area_spin, 3, 1)
        
        # 圆形度范围
        group_layout.addWidget(QLabel("圆形度范围:"), 4, 0)
        circularity_layout = QHBoxLayout()
        self.min_circularity_spin = QDoubleSpinBox()
        self.min_circularity_spin.setRange(0.0, 1.0)
        self.min_circularity_spin.setValue(0.1)
        self.min_circularity_spin.setDecimals(2)
        
        self.max_circularity_spin = QDoubleSpinBox()
        self.max_circularity_spin.setRange(0.0, 1.0)
        self.max_circularity_spin.setValue(0.9)
        self.max_circularity_spin.setDecimals(2)
        
        circularity_layout.addWidget(self.min_circularity_spin)
        circularity_layout.addWidget(QLabel("-"))
        circularity_layout.addWidget(self.max_circularity_spin)
        group_layout.addLayout(circularity_layout, 4, 1)
        
        # 应用参数按钮
        apply_btn = QPushButton("应用参数")
        apply_btn.clicked.connect(self.apply_parameters)
        group_layout.addWidget(apply_btn, 5, 0, 1, 2)
        
        layout.addWidget(group)
    
    def create_detection_options_group(self, layout):
        """创建检测选项组"""
        group = QGroupBox("检测选项")
        group_layout = QVBoxLayout(group)

        # 检测方法选择
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("检测方法:"))
        self.detection_method_combo = QComboBox()
        self.detection_method_combo.addItems([
            "标准检测", "专业路面检测", "专业混凝土检测", "自动选择"
        ])
        method_layout.addWidget(self.detection_method_combo)
        group_layout.addLayout(method_layout)

        # YOLO选项
        self.use_yolo_cb = QCheckBox("使用YOLO分割模型")
        self.use_yolo_cb.setChecked(True)
        group_layout.addWidget(self.use_yolo_cb)

        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("分割模型:"))
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(200)
        model_layout.addWidget(self.model_combo)

        # 模型管理按钮
        self.refresh_models_btn = QPushButton("刷新")
        self.refresh_models_btn.clicked.connect(self.refresh_models)
        model_layout.addWidget(self.refresh_models_btn)

        self.download_model_btn = QPushButton("下载")
        self.download_model_btn.clicked.connect(self.download_selected_model)
        model_layout.addWidget(self.download_model_btn)

        group_layout.addLayout(model_layout)

        # 初始化模型列表
        self.refresh_models()

        # 专业检测选项
        self.use_pavement_cb = QCheckBox("启用专业路面检测 (Canny + Gabor)")
        self.use_pavement_cb.setChecked(False)
        group_layout.addWidget(self.use_pavement_cb)

        self.use_concrete_cb = QCheckBox("启用专业混凝土检测 (完整长度/宽度/面积)")
        self.use_concrete_cb.setChecked(False)
        group_layout.addWidget(self.use_concrete_cb)

        # 宽度计算选项
        self.calculate_width_cb = QCheckBox("计算裂缝宽度 (传统方法)")
        self.calculate_width_cb.setChecked(False)
        group_layout.addWidget(self.calculate_width_cb)

        # 保存结果选项
        self.save_results_cb = QCheckBox("保存检测结果")
        self.save_results_cb.setChecked(True)
        group_layout.addWidget(self.save_results_cb)

        # 显示调试图像选项
        self.show_debug_cb = QCheckBox("显示调试图像")
        self.show_debug_cb.setChecked(False)
        group_layout.addWidget(self.show_debug_cb)

        # 连接信号
        self.detection_method_combo.currentTextChanged.connect(self.on_detection_method_changed)

        layout.addWidget(group)

    def on_detection_method_changed(self, method_text):
        """检测方法改变时的处理"""
        if method_text == "专业路面检测":
            self.use_pavement_cb.setChecked(True)
            self.use_concrete_cb.setChecked(False)
        elif method_text == "专业混凝土检测":
            self.use_pavement_cb.setChecked(False)
            self.use_concrete_cb.setChecked(True)
        elif method_text == "自动选择":
            self.use_pavement_cb.setChecked(True)
            self.use_concrete_cb.setChecked(True)
        else:  # 标准检测
            self.use_pavement_cb.setChecked(False)
            self.use_concrete_cb.setChecked(False)

    def refresh_models(self):
        """刷新模型列表"""
        self.model_combo.clear()

        try:
            from model_manager import model_manager

            # 添加自动选择选项
            self.model_combo.addItem("自动选择", "auto")

            # 获取可用模型
            models = model_manager.get_available_models()
            installed = model_manager.get_installed_models()

            for model_id, config in models.items():
                status = "✅" if model_id in installed else "❌"
                display_name = f"{status} {config['name']} ({config['size_mb']}MB)"
                self.model_combo.addItem(display_name, model_id)

            # 设置默认选择
            recommended = model_manager.recommend_model("balanced")
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == recommended:
                    self.model_combo.setCurrentIndex(i)
                    break

        except ImportError:
            self.model_combo.addItem("模型管理器不可用", None)
            self.download_model_btn.setEnabled(False)

    def download_selected_model(self):
        """下载选中的模型"""
        model_id = self.model_combo.currentData()
        if not model_id or model_id == "auto":
            QMessageBox.information(self, "提示", "请选择要下载的模型")
            return

        try:
            from model_manager import model_manager

            if model_manager.is_model_installed(model_id):
                QMessageBox.information(self, "提示", "模型已安装")
                return

            # 显示下载对话框
            reply = QMessageBox.question(
                self, "确认下载",
                f"确定要下载模型 {model_id} 吗？\n这可能需要一些时间。",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 创建进度对话框
                progress = QProgressDialog("正在下载模型...", "取消", 0, 0, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.show()

                # 在后台下载
                success = model_manager.download_model(model_id)
                progress.close()

                if success:
                    QMessageBox.information(self, "成功", f"模型 {model_id} 下载完成！")
                    self.refresh_models()
                    # 重新初始化检测器
                    self.init_detector()
                else:
                    QMessageBox.warning(self, "失败", f"模型 {model_id} 下载失败")

        except ImportError:
            QMessageBox.warning(self, "错误", "模型管理器不可用")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下载失败: {str(e)}")

    def create_action_buttons_group(self, layout):
        """创建操作按钮组"""
        group = QGroupBox("操作")
        group_layout = QVBoxLayout(group)
        
        # 开始检测按钮
        self.detect_btn = QPushButton("开始检测")
        self.detect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.detect_btn.clicked.connect(self.start_detection)
        group_layout.addWidget(self.detect_btn)
        
        # 批量处理按钮
        self.batch_btn = QPushButton("批量处理")
        self.batch_btn.clicked.connect(self.start_batch_processing)
        group_layout.addWidget(self.batch_btn)
        
        # 停止处理按钮
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_processing)
        group_layout.addWidget(self.stop_btn)
        
        # 清除结果按钮
        clear_btn = QPushButton("清除结果")
        clear_btn.clicked.connect(self.clear_results)
        group_layout.addWidget(clear_btn)
        
        layout.addWidget(group)
    
    def create_results_display_group(self, layout):
        """创建结果显示组"""
        group = QGroupBox("检测结果")
        group_layout = QVBoxLayout(group)
        
        # 结果文本显示
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        self.results_text.setReadOnly(True)
        group_layout.addWidget(self.results_text)
        
        layout.addWidget(group)
    
    def create_display_area(self, parent):
        """创建右侧显示区域"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 原始图像标签页
        self.original_tab = self.create_image_tab("原始图像")
        self.tab_widget.addTab(self.original_tab, "原始图像")
        
        # 检测结果标签页
        self.result_tab = self.create_image_tab("检测结果")
        self.tab_widget.addTab(self.result_tab, "检测结果")
        
        # 宽度结果标签页
        self.width_tab = self.create_image_tab("宽度结果")
        self.tab_widget.addTab(self.width_tab, "宽度结果")
        
        display_layout.addWidget(self.tab_widget)
        parent.addWidget(display_widget)
    
    def create_image_tab(self, title):
        """创建图像显示标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 图像显示标签
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setMinimumSize(800, 600)
        image_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        image_label.setText(f"等待{title}...")
        
        # 添加到滚动区域
        scroll = QScrollArea()
        scroll.setWidget(image_label)
        scroll.setWidgetResizable(True)
        
        layout.addWidget(scroll)
        
        # 保存引用
        if title == "原始图像":
            self.original_image_label = image_label
        elif title == "检测结果":
            self.result_image_label = image_label
        elif title == "宽度结果":
            self.width_image_label = image_label
        
        return tab

    def init_detector(self):
        """初始化检测器"""
        try:
            # 获取选中的模型
            model_id = None
            yolo_model_path = None

            if hasattr(self, 'model_combo'):
                selected_model = self.model_combo.currentData()
                if selected_model and selected_model != "auto":
                    model_id = selected_model

            self.detector = EnhancedCrackDetector(
                scale_factor=0.1,
                unit='mm²',
                yolo_model_path=yolo_model_path,
                yolo_model_id=model_id,
                enable_pavement_detection=True,
                enable_concrete_detection=True
            )

            # 初始化最优分析系统
            self.optimal_system = OptimalCrackAnalysisSystem(
                scale_factor=0.1,
                unit='mm'
            )

            # 显示当前模型信息
            model_info = self.detector.get_current_model_info()
            if model_info:
                status_msg = f"检测器初始化成功 - 模型: {model_info['name']}"
            else:
                status_msg = "检测器初始化成功 (仅传统方法)"

            self.statusBar().showMessage(status_msg)

        except Exception as e:
            QMessageBox.warning(self, "警告", f"检测器初始化失败: {str(e)}")
            self.statusBar().showMessage("检测器初始化失败")

    def browse_image(self):
        """浏览选择图像文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像文件", "",
            "图像文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.tif);;所有文件 (*)"
        )

        if file_path:
            self.image_path_edit.setText(file_path)
            self.current_image_path = file_path
            self.load_and_display_image(file_path)

    def browse_folder(self):
        """浏览选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹")

        if folder_path:
            self.folder_path_edit.setText(folder_path)

    def load_and_display_image(self, image_path):
        """加载并显示图像"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                QMessageBox.warning(self, "错误", "无法读取图像文件")
                return

            # 转换为RGB格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 显示原始图像
            self.display_image(image_rgb, self.original_image_label)

            # 清除之前的结果
            self.result_image_label.setText("等待检测结果...")
            self.width_image_label.setText("等待宽度结果...")

            self.statusBar().showMessage(f"已加载图像: {os.path.basename(image_path)}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载图像失败: {str(e)}")

    def display_image(self, image, label):
        """在标签中显示图像"""
        try:
            height, width, channel = image.shape
            bytes_per_line = 3 * width

            q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # 缩放图像以适应标签大小
            label_size = label.size()
            scaled_pixmap = QPixmap.fromImage(q_image).scaled(
                label_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )

            label.setPixmap(scaled_pixmap)

        except Exception as e:
            print(f"显示图像失败: {e}")
            label.setText("图像显示失败")

    def apply_parameters(self):
        """应用参数设置"""
        if not self.detector:
            QMessageBox.warning(self, "警告", "检测器未初始化")
            return

        try:
            # 更新比例尺和单位
            scale_factor = self.scale_factor_spin.value()
            unit = self.unit_combo.currentText()

            self.detector.set_scale_factor(scale_factor)
            self.detector.set_unit(unit)

            # 更新轮廓筛选参数
            self.detector.update_contour_filter_params(
                min_area=self.min_area_spin.value(),
                max_area=self.max_area_spin.value(),
                min_circularity=self.min_circularity_spin.value(),
                max_circularity=self.max_circularity_spin.value()
            )

            self.statusBar().showMessage("参数已更新")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"参数更新失败: {str(e)}")

    def start_detection(self):
        """开始检测"""
        if not self.current_image_path:
            QMessageBox.warning(self, "警告", "请先选择图像文件")
            return

        if not self.detector:
            QMessageBox.warning(self, "警告", "检测器未初始化")
            return

        # 应用当前参数
        self.apply_parameters()

        # 禁用按钮
        self.detect_btn.setEnabled(False)
        self.batch_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 确定检测方法
        use_pavement = self.use_pavement_cb.isChecked()
        use_concrete = self.use_concrete_cb.isChecked()

        # 获取分析模式
        mode_text = self.analysis_mode_combo.currentText()
        analysis_mode = None
        if mode_text == "快速模式":
            analysis_mode = AnalysisMode.FAST
        elif mode_text == "平衡模式":
            analysis_mode = AnalysisMode.BALANCED
        elif mode_text == "精确模式":
            analysis_mode = AnalysisMode.PRECISE
        elif mode_text == "研究模式":
            analysis_mode = AnalysisMode.RESEARCH

        # 创建处理线程
        self.processing_thread = ImageProcessingThread(
            self.detector,
            self.current_image_path,
            use_yolo=self.use_yolo_cb.isChecked(),
            use_pavement_detection=use_pavement,
            use_concrete_detection=use_concrete,
            calculate_width=self.calculate_width_cb.isChecked(),
            optimal_system=self.optimal_system,
            analysis_mode=analysis_mode
        )

        # 连接信号
        self.processing_thread.finished.connect(self.on_detection_finished)
        self.processing_thread.progress.connect(self.progress_bar.setValue)
        self.processing_thread.status_update.connect(self.statusBar().showMessage)

        # 启动线程
        self.processing_thread.start()

    def start_batch_processing(self):
        """开始批量处理"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "警告", "请先选择图片文件夹")
            return

        if not self.detector:
            QMessageBox.warning(self, "警告", "检测器未初始化")
            return

        # 应用当前参数
        self.apply_parameters()

        # 禁用按钮
        self.detect_btn.setEnabled(False)
        self.batch_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建批量处理线程
        self.batch_thread = BatchProcessingThread(
            self.detector,
            folder_path,
            use_yolo=self.use_yolo_cb.isChecked()
        )

        # 连接信号
        self.batch_thread.finished.connect(self.on_batch_finished)
        self.batch_thread.progress.connect(self.progress_bar.setValue)
        self.batch_thread.status_update.connect(self.statusBar().showMessage)

        # 启动线程
        self.batch_thread.start()

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()

        if self.batch_thread and self.batch_thread.isRunning():
            self.batch_thread.terminate()
            self.batch_thread.wait()

        self.reset_ui_state()
        self.statusBar().showMessage("处理已停止")

    def reset_ui_state(self):
        """重置UI状态"""
        self.detect_btn.setEnabled(True)
        self.batch_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)

    def on_detection_finished(self, result):
        """检测完成回调"""
        self.reset_ui_state()
        self.current_result = result

        if 'error' in result:
            QMessageBox.critical(self, "错误", result['error'])
            self.results_text.setText(f"检测失败: {result['error']}")
            return

        # 显示检测结果
        self.display_detection_results(result)

        self.statusBar().showMessage("检测完成")

    def display_detection_results(self, result):
        """显示检测结果"""
        try:
            # 显示结果图像
            result_image = result['result_image']
            result_image_rgb = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)
            self.display_image(result_image_rgb, self.result_image_label)

            # 显示宽度结果（如果有）
            if 'width_visualization' in result:
                width_image = result['width_visualization']
                width_image_rgb = cv2.cvtColor(width_image, cv2.COLOR_BGR2RGB)
                self.display_image(width_image_rgb, self.width_image_label)

            # 显示文本结果
            self.display_text_results(result)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示结果失败: {str(e)}")

    def display_text_results(self, result):
        """显示文本结果"""
        try:
            area_info = result['area_info']
            text_result = []

            text_result.append("=== 检测结果 ===")
            text_result.append(f"图像路径: {result['image_path']}")
            text_result.append(f"处理时间: {result['processing_time']:.2f}秒")
            text_result.append(f"检测方法: {result['detection_method']}")
            text_result.append("")

            text_result.append("=== 面积信息 ===")
            text_result.append(f"裂缝数量: {area_info['contour_count']}")
            text_result.append(f"总像素面积: {area_info['total_pixel_area']:.0f} 像素²")
            text_result.append(f"总实际面积: {area_info['total_real_area']:.2f} {result['unit']}")

            if area_info['contour_count'] > 0:
                stats = area_info['area_statistics']
                text_result.append(f"平均面积: {stats['mean_real_area']:.2f} {result['unit']}")
                text_result.append(f"最大面积: {stats['max_real_area']:.2f} {result['unit']}")
                text_result.append(f"最小面积: {stats['min_real_area']:.2f} {result['unit']}")
                text_result.append(f"面积标准差: {stats['std_real_area']:.2f} {result['unit']}")

            # 宽度信息（如果有）
            if 'width_info' in result:
                width_info = result['width_info']
                text_result.append("")
                text_result.append("=== 宽度信息 ===")
                text_result.append(f"宽度测量点数: {width_info['width_count']}")

                if width_info['width_count'] > 0:
                    stats = width_info['statistics']
                    unit = result['unit'].replace('²', '')
                    text_result.append(f"平均宽度: {stats['mean_width']:.3f} {unit}")
                    text_result.append(f"最大宽度: {stats['max_width']:.3f} {unit}")
                    text_result.append(f"最小宽度: {stats['min_width']:.3f} {unit}")
                    text_result.append(f"宽度标准差: {stats['std_width']:.3f} {unit}")
                    text_result.append(f"骨架线长度: {width_info['total_length_real']:.2f} {unit}")

            text_result.append("")
            text_result.append(f"比例尺: 1像素 = {result['scale_factor']:.3f}{result['unit'].replace('²', '')}")

            if result.get('save_path'):
                text_result.append(f"结果已保存至: {result['save_path']}")

            self.results_text.setText("\n".join(text_result))

        except Exception as e:
            self.results_text.setText(f"结果显示失败: {str(e)}")

    def on_batch_finished(self, results):
        """批量处理完成回调"""
        self.reset_ui_state()

        if not results:
            QMessageBox.warning(self, "警告", "批量处理失败或无结果")
            return

        # 统计结果
        successful = sum(1 for r in results if 'error' not in r)
        failed = len(results) - successful

        # 显示统计信息
        stats_text = []
        stats_text.append("=== 批量处理结果 ===")
        stats_text.append(f"总计: {len(results)} 张图像")
        stats_text.append(f"成功: {successful} 张")
        stats_text.append(f"失败: {failed} 张")
        stats_text.append("")

        if successful > 0:
            # 计算总体统计
            total_cracks = 0
            total_area = 0

            for result in results:
                if 'error' not in result:
                    area_info = result['area_info']
                    total_cracks += area_info['contour_count']
                    total_area += area_info['total_real_area']

            stats_text.append(f"总检测裂缝数: {total_cracks}")
            stats_text.append(f"总裂缝面积: {total_area:.2f} {self.detector.unit}")
            stats_text.append(f"平均每张图像裂缝数: {total_cracks/successful:.1f}")
            stats_text.append(f"平均每张图像面积: {total_area/successful:.2f} {self.detector.unit}")

        stats_text.append("")
        stats_text.append("详细结果已保存至CSV文件")

        self.results_text.setText("\n".join(stats_text))

        QMessageBox.information(self, "完成", f"批量处理完成！\n成功: {successful} 张\n失败: {failed} 张")
        self.statusBar().showMessage("批量处理完成")

    def clear_results(self):
        """清除结果"""
        self.result_image_label.setText("等待检测结果...")
        self.width_image_label.setText("等待宽度结果...")
        self.results_text.clear()
        self.current_result = None
        self.statusBar().showMessage("结果已清除")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有线程
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()

        if self.batch_thread and self.batch_thread.isRunning():
            self.batch_thread.terminate()
            self.batch_thread.wait()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("增强版裂缝检测系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("CrackDetection")

    # 设置全局字体为微软雅黑
    setup_font()

    # 创建主窗口
    window = EnhancedCrackDetectionGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
