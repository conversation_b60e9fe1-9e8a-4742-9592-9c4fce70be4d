"""
混凝土裂缝宽度检测系统
功能:
1. 测量裂缝宽度(像素级和物理尺寸)
2. 支持批量处理和CSV报告生成

使用方法:
文件夹批量: python crack_width_detector.py --source path/to/folder --scale 0.1
"""

import numpy as np
import os
import glob
from pathlib import Path
import matplotlib.pyplot as plt
class CrackWidthDetector:
    """初始化YOLOv11模型"""
    def __init__(self):
        self.scale_factor = 1.0  # 像素到毫米的转换系数

    def set_scale(self, scale):
        self.scale_factor = scale

    def detect_cracks(self, image_path, conf_thres=0.25):
        # 读取图像
        if image is None:
            return None, None
        # 使用YOLOv11进行检测
        
        if results.masks is not None:
            mask = (mask * 255).astype(np.uint8)
        return image, None
    def measure_width_and_area(self, image, mask):
        """
        测量裂缝宽度和面积（像素级和物理尺寸）
        参数:
            image: 原始图像
            mask: 裂缝掩模
        返回:
            result: 标注后的图像
            measurements: 包含宽度和面积数据的字典
        """
        if mask is None:
            return image, None
            
        # 边缘检测
        edges = cv2.Canny(mask, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 测量宽度和面积
        width_measurements = []
        area_measurements = []
        max_width = 0
        min_width = float('inf')
        total_area = 0
        
        for cnt in contours:
            # 计算最小外接矩形
            rect = cv2.minAreaRect(cnt)
            width = min(rect[1])
            
            # 计算轮廓面积
            area = cv2.contourArea(cnt)
            
            if width > 0:  # 过滤无效测量
                width_measurements.append(width)
                area_measurements.append(area)
                max_width = max(max_width, width)
                min_width = min(min_width, width)
                total_area += area
        
        # 计算统计信息
        avg_width = np.mean(width_measurements) if width_measurements else 0
        std_width = np.std(width_measurements) if len(width_measurements) > 1 else 0
        avg_area = np.mean(area_measurements) if area_measurements else 0
        
        # 可视化结果
        result = image.copy()
        cv2.drawContours(result, contours, -1, (0, 255, 0), 2)
        
        # 标注信息
        text_max = f"最大宽度: {max_width:.2f}px ({max_width*self.scale_factor:.2f}mm)"
        text_min = f"最小宽度: {min_width:.2f}px ({min_width*self.scale_factor:.2f}mm)"
        text_avg = f"平均宽度: {avg_width:.2f}px ({avg_width*self.scale_factor:.2f}mm)"
        text_area = f"总面积: {total_area:.2f}px² ({total_area*(self.scale_factor**2):.2f}mm²)"
        
        cv2.putText(result, text_max, (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.putText(result, text_min, (20, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(result, text_avg, (20, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        cv2.putText(result, text_area, (20, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
        measurements = {
            'max': max_width,
            'min': min_width,
            'avg': avg_width,
            'std': std_width,
            'total_area': total_area,
            'avg_area': avg_area,
            'unit': 'mm',
            'area_unit': 'mm²'
        }
        
        return result, measurements
            'min': min_width,
            'count': len(width_measurements)
        
    
        """处理单张图片"""
        image, mask = self.detect_cracks(image_path, conf_thres)
            print(f"未检测到裂缝: {image_path}")
        
        result, width_stats = self.measure_width(image, mask)
        # 保存结果
        if output_dir:
            filename = Path(image_path).stem + "_width.jpg"
            cv2.imwrite(output_path, result)
        
        if show:
            cv2.waitKey(0)
        
    
        """批量处理目录中的图片"""
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
        
        for ext in image_extensions:
        # 支持的图片格式
        if not image_files:
            return None
        # 创建CSV报告
        if output_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
                csv_writer = csv.writer(csvfile)
                                    '最大宽度(像素)', '最大宽度(mm)',
                                    '标准差', '测量点数'])
        # 处理结果存储
        
        for i, img_path in enumerate(image_files):
            result, width_stats, output_path = self.process_image(
            )
            if width_stats:
                results.append({
                    'stats': width_stats,
                })
                # 写入CSV
                    with open(csv_path, 'a', newline='') as csvfile:
                        csv_writer.writerow([
                            f"{width_stats['avg']:.2f}",
                            f"{width_stats['max']:.2f}",
                            f"{width_stats['min']:.2f}",
                            f"{width_stats['std']:.2f}",
                        ])
        # 生成汇总图表
            self.generate_summary_chart(results, output_dir)
        return results, csv_path
    def generate_summary_chart(self, results, output_dir):
        if not results:
            
        filenames = [os.path.basename(r['file']) for r in results]
        max_widths = [r['stats']['max'] for r in results]
        
        avg_widths_mm = [w * self.scale_factor for w in avg_widths]
        min_widths_mm = [w * self.scale_factor for w in min_widths]
        # 创建图表
        
        plt.subplot(2, 1, 1)
        plt.title('裂缝平均宽度 (mm)')
        plt.tight_layout()
        # 最大/最小宽度图表
        x = np.arange(len(filenames))
        plt.bar(x - width/2, max_widths_mm, width, label='最大宽度', color='red', alpha=0.7)
        plt.title('裂缝最大/最小宽度 (mm)')
        plt.legend()
        
        chart_path = os.path.join(output_dir, 'crack_width_summary.png')
        plt.xticks(x, filenames, rotation=45, ha='right')

    """检查路径是否为目录"""

    parser = argparse.ArgumentParser(description="混凝土裂缝宽度检测")
    parser.add_argument("--model", type=str, default="yolo11n-seg.pt", help="YOLOv11模型路径")
    parser.add_argument("--conf", type=float, default=0.25, help="置信度阈值")
    parser.add_argument("--show", action="store_true", help="显示结果")
    
    detector.set_scale(args.scale)
    # 检查输入是文件还是目录
        # 批量处理目录
            args.source,
            show=args.show,
        )
        if results:
            print(f"详细报告已保存至: {csv_path}")
            print("未检测到任何裂缝")
        # 处理单张图片
            args.source, 
            show=args.show,
        )
        if result is not None and width_stats is not None:
            print(f"  平均宽度: {width_stats['avg']:.2f}像素 ({width_stats['avg']*args.scale:.2f}毫米)")
            print(f"  最小宽度: {width_stats['min']:.2f}像素 ({width_stats['min']*args.scale:.2f}毫米)")
            print(f"  测量点数: {width_stats['count']}")