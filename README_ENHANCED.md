# 增强版裂缝检测系统 v2.0

基于YOLO分割模型和传统图像处理技术的智能裂缝检测系统，支持面积计算和宽度测量。

## 🚀 主要特性

### 🔍 **双重检测策略**
- **YOLO分割模型**: 高精度深度学习检测
- **传统图像处理**: 多层次预处理和边缘检测
- **智能融合**: 自动结合两种方法的优势

### 🖼️ **多层次图像预处理**
- **灰度化** → **Gamma校正** → **中值滤波** → **双边滤波**
- **CLAHE对比度增强**: 自适应直方图均衡化
- **鲁棒性强**: 适应不同光照条件和图像质量

### 🎯 **智能轮廓筛选**
- **面积筛选**: 基于最小/最大面积阈值
- **圆形度筛选**: 排除非裂缝形状的噪声
- **长宽比筛选**: 识别细长的裂缝特征
- **双重筛选机制**: 提高检测精度

### 📏 **精确面积计算**
- **像素面积**: 精确计算轮廓像素面积
- **实际面积**: 支持比例尺转换
- **统计分析**: 均值、最值、标准差等
- **个体分析**: 每个裂缝的独立面积计算

### 📐 **裂缝宽度计算**
- **正交骨架线法**: 基于中轴变换的宽度计算
- **法向量估计**: SVD方法计算骨架线法向量
- **边缘检测**: 精确提取裂缝边缘线
- **宽度统计**: 平均、最大、最小宽度分析

### 📊 **完整可视化**
- **标注裂缝轮廓**: 彩色轮廓标记
- **面积信息显示**: 实时显示统计数据
- **宽度测量线**: 可视化宽度测量位置
- **调试图像**: 完整的处理步骤可视化

### 📈 **数据输出**
- **数值结果**: 像素面积、实际面积、裂缝数量
- **CSV报告**: 批量处理的详细统计报告
- **图像保存**: 标注结果和调试图像
- **实时显示**: GUI界面的实时结果展示

## 📦 安装要求

### 基础依赖
```bash
pip install opencv-python numpy matplotlib scikit-image sklearn
pip install PyQt5  # GUI界面需要
pip install ultralytics  # YOLO模型支持
```

### 完整安装
```bash
pip install -r requirements.txt
```

### requirements.txt
```
opencv-python>=4.5.0
numpy>=1.19.0
matplotlib>=3.3.0
scikit-image>=0.18.0
scikit-learn>=0.24.0
PyQt5>=5.15.0
ultralytics>=8.0.0
pathlib
datetime
csv
```

## 🎮 使用方法

### 1. 图形界面使用

#### 启动GUI
```bash
python enhanced_crack_gui.py
```

#### 操作步骤
1. **加载图片**: 点击"浏览图片"选择要检测的图像
2. **调整参数**: 
   - 设置比例尺（像素/实际单位）
   - 调整最小/最大面积阈值
   - 设置圆形度筛选范围
3. **选择选项**:
   - ✅ 使用YOLO分割模型
   - ✅ 计算裂缝宽度
   - ✅ 保存检测结果
4. **开始检测**: 点击"开始检测"执行检测
5. **查看结果**: 在右侧标签页查看结果并保存

#### 批量处理
1. 选择"浏览文件夹"指定图片文件夹
2. 点击"批量处理"开始批量检测
3. 自动生成CSV报告和结果图像

### 2. 命令行使用

#### 基本用法
```bash
# 单张图片检测
python enhanced_crack_detect.py --source test.jpg --scale 0.1

# 文件夹批量检测
python enhanced_crack_detect.py --source images/ --batch --scale 0.05

# 带宽度计算
python enhanced_crack_detect.py --source crack.jpg --width --scale 0.1 --show

# 使用YOLO模型
python enhanced_crack_detect.py --source test.jpg --yolo yolo11n-seg.pt --scale 0.1
```

#### 完整参数说明
```bash
python enhanced_crack_detect.py \
    --source test.jpg \          # 输入图像或文件夹
    --scale 0.1 \               # 比例尺 (像素/实际单位)
    --unit mm² \                # 面积单位
    --yolo yolo11n-seg.pt \     # YOLO模型路径
    --width \                   # 计算宽度
    --batch \                   # 批量处理
    --show \                    # 显示结果
    --output results \          # 输出目录
    --min-area 50 \             # 最小面积阈值
    --max-area 50000 \          # 最大面积阈值
    --verbose                   # 详细输出
```

### 3. Python API使用

#### 基础检测
```python
from crack_detection import EnhancedCrackDetector

# 创建检测器
detector = EnhancedCrackDetector(
    scale_factor=0.1,  # 1像素 = 0.1mm
    unit='mm²',
    yolo_model_path="yolo11n-seg.pt"
)

# 处理单张图像
result = detector.process_single_image(
    "test_crack.jpg",
    output_dir="output",
    use_yolo=True
)

# 查看结果
if 'error' not in result:
    area_info = result['area_info']
    print(f"检测到 {area_info['contour_count']} 个裂缝")
    print(f"总面积: {area_info['total_real_area']:.2f} mm²")
```

#### 宽度计算
```python
from crack_width_calculator import CrackWidthCalculator
import cv2
import numpy as np

# 创建宽度计算器
width_calculator = CrackWidthCalculator(scale_factor=0.1, unit='mm')

# 从检测结果计算宽度
image = cv2.imread("test_crack.jpg")
contours = result['contours']  # 从检测结果获取

# 创建二值掩码
mask = np.zeros(image.shape[:2], dtype=np.uint8)
cv2.fillPoly(mask, contours, 255)

# 计算宽度
width_result = width_calculator.calculate_crack_width(mask)

if width_result['width_count'] > 0:
    stats = width_result['statistics']
    print(f"平均宽度: {stats['mean_width']:.3f} mm")
    print(f"最大宽度: {stats['max_width']:.3f} mm")
```

#### 批量处理
```python
# 批量处理文件夹
results = detector.batch_process_images(
    "test_images/",
    output_dir="batch_results",
    use_yolo=True,
    generate_csv=True
)

# 统计结果
successful = sum(1 for r in results if 'error' not in r)
total_cracks = sum(r['area_info']['contour_count'] for r in results if 'error' not in r)
print(f"成功处理: {successful} 张图像")
print(f"总检测裂缝数: {total_cracks}")
```

## 📁 项目结构

```
enhanced-crack-detection/
├── crack_detection.py          # 核心检测模块
├── crack_width_calculator.py   # 宽度计算模块
├── enhanced_crack_gui.py       # GUI界面
├── enhanced_crack_detect.py    # 命令行工具
├── README_ENHANCED.md          # 项目文档
├── requirements.txt            # 依赖列表
├── test_images/               # 测试图像
├── output/                    # 输出结果
├── models/                    # YOLO模型文件
│   ├── yolo11n-seg.pt
│   ├── yolo11s-seg.pt
│   └── yolo11m-seg.pt
└── examples/                  # 使用示例
    ├── basic_detection.py
    ├── width_calculation.py
    └── batch_processing.py
```

## 🔧 参数配置

### 检测参数
- `scale_factor`: 比例尺，像素到实际单位的转换比例
- `unit`: 面积单位 (mm², cm², m², 像素²)
- `yolo_model_path`: YOLO分割模型路径

### 预处理参数
- `gamma`: Gamma校正系数 (默认: 1.2)
- `median_kernel`: 中值滤波核大小 (默认: 5)
- `bilateral_d`: 双边滤波邻域直径 (默认: 9)

### 轮廓筛选参数
- `min_area`: 最小轮廓面积 (默认: 50像素²)
- `max_area`: 最大轮廓面积 (默认: 50000像素²)
- `min_circularity`: 最小圆形度 (默认: 0.1)
- `max_circularity`: 最大圆形度 (默认: 0.9)
- `min_aspect_ratio`: 最小长宽比 (默认: 2.0)

### 宽度计算参数
- `hband`: 水平带宽 (默认: 2.0)
- `vband`: 垂直带宽 (默认: 2.0)
- `knn_neighbors`: KNN邻居数量 (默认: 5)

## 📊 输出结果

### 面积检测结果
```
=== 检测结果 ===
图像路径: test_crack.jpg
处理时间: 2.34秒
检测方法: Enhanced+YOLO

=== 面积信息 ===
裂缝数量: 3
总像素面积: 15420 像素²
总实际面积: 154.20 mm²
平均面积: 51.40 mm²
最大面积: 89.30 mm²
最小面积: 23.60 mm²
面积标准差: 28.45 mm²
```

### 宽度计算结果
```
=== 宽度信息 ===
宽度测量点数: 45
平均宽度: 2.340 mm
最大宽度: 4.120 mm
最小宽度: 1.230 mm
宽度标准差: 0.680 mm
骨架线长度: 234.50 mm
```

### CSV报告格式
| 图像路径 | 处理时间 | 裂缝数量 | 总像素面积 | 总实际面积 | 平均面积 | 最大面积 | 检测方法 |
|---------|---------|---------|-----------|-----------|---------|---------|---------|
| test1.jpg | 2021-12-01 10:30:15 | 2 | 8500 | 85.00 | 42.50 | 65.30 | Enhanced+YOLO |
| test2.jpg | 2021-12-01 10:30:18 | 1 | 3200 | 32.00 | 32.00 | 32.00 | Enhanced+YOLO |

## 🎯 技术特点

### 算法优势
1. **双重检测策略**: 结合深度学习和传统方法
2. **多层次预处理**: 提高图像质量和检测精度
3. **智能筛选机制**: 减少误检和噪声
4. **精确面积计算**: 支持多种单位转换
5. **宽度测量**: 基于正交骨架线法的精确计算

### 性能特点
- **高精度**: YOLO模型提供高精度分割
- **鲁棒性**: 适应不同光照和图像质量
- **实时性**: 优化的算法确保处理速度
- **可扩展**: 模块化设计便于功能扩展

## 🔍 故障排除

### 常见问题

1. **YOLO模型加载失败**
   ```
   解决方案: 检查模型文件路径，确保ultralytics已安装
   ```

2. **图像读取失败**
   ```
   解决方案: 检查图像文件格式和路径，支持jpg/png/bmp等格式
   ```

3. **检测结果为空**
   ```
   解决方案: 调整参数设置，降低最小面积阈值或调整圆形度范围
   ```

4. **GUI界面无法启动**
   ```
   解决方案: 确保PyQt5已正确安装: pip install PyQt5
   ```

### 性能优化建议

1. **图像预处理**: 使用高质量图像获得更好结果
2. **参数调优**: 根据具体应用场景调整筛选参数
3. **模型选择**: 根据精度需求选择合适的YOLO模型
4. **批量处理**: 大量图像建议使用批量处理模式

## 📝 更新日志

### v2.0 (当前版本)
- ✅ 集成YOLO分割模型
- ✅ 多层次图像预处理
- ✅ 智能轮廓筛选机制
- ✅ 精确面积计算
- ✅ 裂缝宽度计算功能
- ✅ 增强版GUI界面
- ✅ 完整的CSV报告生成
- ✅ 命令行工具支持

### v1.0 (原版本)
- ✅ 基础裂缝检测
- ✅ 简单面积计算
- ✅ 基础可视化

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📧 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub Repository]
- 邮箱: [<EMAIL>]

---

**增强版裂缝检测系统 v2.0** - 让裂缝检测更智能、更精确！
