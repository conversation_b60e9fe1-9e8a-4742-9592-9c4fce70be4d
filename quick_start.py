#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版裂缝检测系统 - 快速启动脚本

这个脚本提供了一个简单的交互式界面来快速开始使用裂缝检测系统。
用户可以选择不同的功能模式，无需记忆复杂的命令行参数。
"""

import os
import sys
import subprocess
from pathlib import Path


def print_banner():
    """打印欢迎横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                增强版裂缝检测系统 v2.0                        ║
    ║                Enhanced Crack Detection System               ║
    ║                                                              ║
    ║  🔍 YOLO分割 + 传统处理  📏 面积计算 + 宽度测量              ║
    ║  🎯 智能筛选 + 精确分析  📊 批量处理 + CSV报告               ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_dependencies():
    """检查依赖包"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'cv2', 'numpy', 'matplotlib', 'sklearn', 'skimage'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'numpy':
                import numpy
            elif package == 'matplotlib':
                import matplotlib
            elif package == 'sklearn':
                import sklearn
            elif package == 'skimage':
                import skimage
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements_enhanced.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True


def check_models():
    """检查YOLO模型文件"""
    print("🔍 检查YOLO模型文件...")
    
    model_files = ['yolo11n-seg.pt', 'yolo11s-seg.pt', 'yolo11m-seg.pt', 'yolo11l-seg.pt']
    available_models = []
    
    for model in model_files:
        if os.path.exists(model):
            available_models.append(model)
    
    if available_models:
        print(f"✅ 找到YOLO模型: {', '.join(available_models)}")
        return available_models[0]  # 返回第一个可用模型
    else:
        print("⚠️  未找到YOLO模型文件")
        print("系统将使用传统图像处理方法")
        return None


def get_test_images():
    """获取测试图像"""
    test_dir = Path("test_images")
    if test_dir.exists():
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(list(test_dir.glob(ext)))
        return [str(f) for f in image_files]
    return []


def show_menu():
    """显示主菜单"""
    menu = """
    ┌─────────────────────────────────────────────────────────────┐
    │                        主菜单                                │
    ├─────────────────────────────────────────────────────────────┤
    │  1. 🖼️  启动图形界面 (推荐)                                  │
    │  2. 📷 单张图片检测                                          │
    │  3. 📁 文件夹批量检测                                        │
    │  4. 📐 裂缝宽度计算                                          │
    │  5. 🧪 运行系统测试                                          │
    │  6. 📖 查看使用说明                                          │
    │  7. ⚙️  系统配置检查                                         │
    │  0. 🚪 退出程序                                              │
    └─────────────────────────────────────────────────────────────┘
    """
    print(menu)


def launch_gui():
    """启动图形界面"""
    print("🚀 启动图形界面...")
    try:
        subprocess.run([sys.executable, "enhanced_crack_gui.py"])
    except FileNotFoundError:
        print("❌ 找不到GUI文件: enhanced_crack_gui.py")
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")


def single_image_detection():
    """单张图片检测"""
    print("\n📷 单张图片检测")
    print("-" * 40)
    
    # 获取图片路径
    image_path = input("请输入图片路径 (或按Enter使用测试图片): ").strip()
    
    if not image_path:
        test_images = get_test_images()
        if test_images:
            image_path = test_images[0]
            print(f"使用测试图片: {image_path}")
        else:
            print("❌ 没有找到测试图片")
            return
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    # 获取参数
    scale = input("请输入比例尺 (默认0.1): ").strip() or "0.1"
    unit = input("请输入面积单位 (默认mm²): ").strip() or "mm²"
    
    # 构建命令
    cmd = [
        sys.executable, "enhanced_crack_detect.py",
        "--source", image_path,
        "--scale", scale,
        "--unit", unit,
        "--show",
        "--verbose"
    ]
    
    print(f"🔍 开始检测: {image_path}")
    try:
        subprocess.run(cmd)
    except Exception as e:
        print(f"❌ 检测失败: {e}")


def batch_processing():
    """文件夹批量检测"""
    print("\n📁 文件夹批量检测")
    print("-" * 40)
    
    # 获取文件夹路径
    folder_path = input("请输入图片文件夹路径 (或按Enter使用test_images): ").strip()
    
    if not folder_path:
        folder_path = "test_images"
        print(f"使用默认文件夹: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return
    
    # 获取参数
    scale = input("请输入比例尺 (默认0.1): ").strip() or "0.1"
    unit = input("请输入面积单位 (默认mm²): ").strip() or "mm²"
    
    # 构建命令
    cmd = [
        sys.executable, "enhanced_crack_detect.py",
        "--source", folder_path,
        "--batch",
        "--scale", scale,
        "--unit", unit,
        "--csv",
        "--verbose"
    ]
    
    print(f"🔍 开始批量处理: {folder_path}")
    try:
        subprocess.run(cmd)
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")


def width_calculation():
    """裂缝宽度计算"""
    print("\n📐 裂缝宽度计算")
    print("-" * 40)
    
    # 获取图片路径
    image_path = input("请输入图片路径 (或按Enter使用测试图片): ").strip()
    
    if not image_path:
        test_images = get_test_images()
        if test_images:
            image_path = test_images[0]
            print(f"使用测试图片: {image_path}")
        else:
            print("❌ 没有找到测试图片")
            return
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    # 获取参数
    scale = input("请输入比例尺 (默认0.1): ").strip() or "0.1"
    
    # 构建命令
    cmd = [
        sys.executable, "enhanced_crack_detect.py",
        "--source", image_path,
        "--scale", scale,
        "--width",
        "--show",
        "--verbose"
    ]
    
    print(f"🔍 开始宽度计算: {image_path}")
    try:
        subprocess.run(cmd)
    except Exception as e:
        print(f"❌ 宽度计算失败: {e}")


def run_tests():
    """运行系统测试"""
    print("\n🧪 运行系统测试")
    print("-" * 40)
    
    try:
        subprocess.run([sys.executable, "test_enhanced_system.py"])
    except FileNotFoundError:
        print("❌ 找不到测试文件: test_enhanced_system.py")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")


def show_help():
    """显示使用说明"""
    help_text = """
    📖 使用说明
    ═══════════════════════════════════════════════════════════════
    
    🎯 系统功能:
    • YOLO分割模型 + 传统图像处理双重检测
    • 多层次图像预处理 (灰度化→Gamma校正→滤波)
    • 智能轮廓筛选 (面积+圆形度+长宽比)
    • 精确面积计算 (像素→实际面积转换)
    • 裂缝宽度计算 (正交骨架线法)
    • 批量处理 + CSV报告生成
    
    🚀 快速开始:
    1. 选择菜单选项1启动图形界面 (最简单)
    2. 或选择其他选项使用命令行功能
    
    📁 文件结构:
    • crack_detection.py - 核心检测模块
    • crack_width_calculator.py - 宽度计算模块
    • enhanced_crack_gui.py - 图形界面
    • enhanced_crack_detect.py - 命令行工具
    • test_enhanced_system.py - 测试脚本
    
    ⚙️ 参数说明:
    • 比例尺: 1像素对应的实际长度 (如0.1表示1像素=0.1mm)
    • 面积单位: mm², cm², m²等
    • 最小面积: 过滤小噪声的面积阈值
    • 圆形度: 0-1之间，用于筛选裂缝形状
    
    📞 获取帮助:
    • 查看README_ENHANCED.md获取详细文档
    • 运行测试脚本检查系统状态
    • 使用--help参数查看命令行帮助
    """
    print(help_text)


def system_check():
    """系统配置检查"""
    print("\n⚙️ 系统配置检查")
    print("-" * 40)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查依赖
    check_dependencies()
    
    # 检查模型
    model = check_models()
    
    # 检查测试图像
    test_images = get_test_images()
    if test_images:
        print(f"✅ 找到 {len(test_images)} 张测试图像")
    else:
        print("⚠️  未找到测试图像")
    
    # 检查输出目录
    output_dir = Path("output")
    if output_dir.exists():
        print("✅ 输出目录存在")
    else:
        print("⚠️  输出目录不存在，将自动创建")
        output_dir.mkdir(exist_ok=True)
    
    print("\n系统检查完成！")


def main():
    """主函数"""
    print_banner()
    
    # 基础检查
    if not check_dependencies():
        print("\n请先安装依赖包后再运行此程序")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能 (0-7): ").strip()
            
            if choice == '0':
                print("👋 感谢使用增强版裂缝检测系统！")
                break
            elif choice == '1':
                launch_gui()
            elif choice == '2':
                single_image_detection()
            elif choice == '3':
                batch_processing()
            elif choice == '4':
                width_calculation()
            elif choice == '5':
                run_tests()
            elif choice == '6':
                show_help()
            elif choice == '7':
                system_check()
            else:
                print("❌ 无效选择，请输入0-7之间的数字")
            
            if choice != '0':
                input("\n按Enter键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按Enter键继续...")


if __name__ == "__main__":
    main()
