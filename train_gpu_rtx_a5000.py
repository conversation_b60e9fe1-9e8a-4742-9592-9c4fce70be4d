# -*- coding: utf-8 -*-
"""
RTX A5000 GPU Training Script for YOLO Crack Detection
Optimized for 24GB VRAM
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')

import torch
from ultralytics import <PERSON>OL<PERSON>

def main():
    """Main training function"""
    print("=== RTX A5000 GPU Training Started ===")
    
    # Check GPU status
    print(f"PyTorch Version: {torch.__version__}")
    print(f"CUDA Available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU: {gpu_name} ({memory_gb:.1f} GB)")
        
        # RTX A5000 24GB optimized configuration
        config = {
            'device': '0',
            'batch': 16,        # Large VRAM supports large batch
            'workers': 8,       # Multi-threaded data loading
            'cache': True,      # Enable caching
            'amp': True,        # Mixed precision training
            'optimizer': 'AdamW'
        }
        print("Using RTX A5000 optimized configuration")
    else:
        print("ERROR: CUDA not available")
        return
    
    # Load model
    model_path = r'E:\yolo\ultralytics-main\ultralytics-main-max-area\yolo11n-seg.pt'
    if os.path.exists(model_path):
        model = YOLO(model_path)
        print(f"Loaded local model: {model_path}")
    else:
        model = YOLO('yolo11n-seg.pt')
        print("Downloaded and loaded model: yolo11n-seg.pt")
    
    # Training parameters
    train_args = {
        'data': r'E:\yolo\ultralytics-main\ultralytics-main-max-area\data.yaml',
        'imgsz': 640,
        'epochs': 300,
        'batch': config['batch'],
        'workers': config['workers'],
        'device': config['device'],
        'optimizer': config['optimizer'],
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'close_mosaic': 10,
        'resume': False,
        'project': 'runs/train',
        'name': 'crack_detection_rtx_a5000',
        'single_cls': False,
        'cache': config['cache'],
        'amp': config['amp'],
        'save_period': 10,
        'patience': 50,
        'verbose': True,
        'plots': True,
        'val': True,
    }
    
    print(f"\nStarting GPU training...")
    print(f"Device: {config['device']}")
    print(f"Batch size: {config['batch']}")
    print(f"Workers: {config['workers']}")
    print(f"Mixed precision: {config['amp']}")
    print(f"Optimizer: {config['optimizer']}")
    print(f"Epochs: {train_args['epochs']}")
    
    try:
        # Clear GPU cache
        torch.cuda.empty_cache()
        
        # Start training
        print("\n" + "="*50)
        print("TRAINING STARTED...")
        print("="*50)
        
        results = model.train(**train_args)
        
        print("\n" + "="*50)
        print("TRAINING COMPLETED SUCCESSFULLY!")
        print(f"Best model saved at: {results.save_dir}")
        print("="*50)
        
    except Exception as e:
        print(f"\nTRAINING FAILED: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clear GPU cache
        torch.cuda.empty_cache()
        print("\nGPU cache cleared")

if __name__ == '__main__':
    main()
