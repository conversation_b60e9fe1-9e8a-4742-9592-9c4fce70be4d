# -*- coding: utf-8 -*-
"""
自动优化的YOLO训练脚本
根据系统配置自动调整参数
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')
import torch
from ultralytics import YOLO

def main():
    """主训练函数"""
    print("=== YOLO裂缝检测模型训练 ===")
    
    # 检测设备
    if torch.cuda.is_available():
        print(f"✅ 使用GPU训练: {torch.cuda.get_device_name(0)}")
    else:
        print("💻 使用CPU训练")
    
    # 加载模型
    model_path = r'E:\yolo\ultralytics-main\ultralytics-main-max-area\yolo11n-seg.pt'
    if not os.path.exists(model_path):
        print("⚠️  模型文件不存在，将自动下载")
        model = YOLO('yolo11n-seg.pt')
    else:
        model = YOLO(model_path)
    
    # 训练配置
    config = {
        'data': r'E:\yolo\ultralytics-main\ultralytics-main-max-area\data.yaml',
        'imgsz': 640,
        'epochs': 50,
        'batch': 2,
        'workers': 0,
        'device': 'cpu',
        'optimizer': 'SGD',
        'close_mosaic': 10,
        'resume': False,
        'project': 'runs/train',
        'name': 'crack_detection',
        'single_cls': False,
        'cache': False,
        'save_period': 10,  # 每10轮保存一次
        'patience': 50,     # 早停耐心值
        'verbose': True
    }
    
    print("\n训练参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\n🚀 开始训练...")
    try:
        results = model.train(**config)
        print("✅ 训练完成!")
        print(f"最佳模型保存在: {results.save_dir}")
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()
