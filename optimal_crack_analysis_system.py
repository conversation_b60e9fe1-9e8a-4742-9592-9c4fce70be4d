# -*- coding: utf-8 -*-
"""
最优裂缝分析系统 - 项目最优解集成

功能：
1. 智能分析流程 - 自动选择最佳检测策略
2. 多维度评估 - 面积、长度、宽度、形态学特征
3. 质量控制 - 结果可靠性评估和质量保证
4. 智能报告 - 自动生成专业分析报告
5. 性能优化 - 自适应参数调整和性能监控
"""

import cv2
import numpy as np
import os
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from advanced_segmentation_system import AdvancedSegmentationSystem
from crack_detection import put_chinese_text

class AnalysisMode(Enum):
    """分析模式枚举"""
    FAST = "fast"           # 快速模式
    BALANCED = "balanced"   # 平衡模式  
    PRECISE = "precise"     # 精确模式
    RESEARCH = "research"   # 研究模式

@dataclass
class CrackFeatures:
    """裂缝特征数据类"""
    area: float
    length: float
    width: float
    aspect_ratio: float
    circularity: float
    solidity: float
    orientation: float
    roughness: float

class OptimalCrackAnalysisSystem:
    """最优裂缝分析系统"""
    
    def __init__(self, scale_factor: float = 0.1, unit: str = 'mm'):
        """
        初始化最优分析系统
        
        Args:
            scale_factor: 像素到实际单位的转换比例
            unit: 测量单位
        """
        self.scale_factor = scale_factor
        self.unit = unit
        
        # 初始化高级分割系统
        self.segmentation_system = AdvancedSegmentationSystem(scale_factor, unit + '²')
        
        # 分析参数配置
        self.analysis_configs = {
            AnalysisMode.FAST: {
                'confidence_threshold': 0.3,
                'min_area_threshold': 50,
                'enable_morphology': False,
                'enable_width_analysis': False,
                'enable_quality_check': False
            },
            AnalysisMode.BALANCED: {
                'confidence_threshold': 0.25,
                'min_area_threshold': 30,
                'enable_morphology': True,
                'enable_width_analysis': True,
                'enable_quality_check': True
            },
            AnalysisMode.PRECISE: {
                'confidence_threshold': 0.2,
                'min_area_threshold': 20,
                'enable_morphology': True,
                'enable_width_analysis': True,
                'enable_quality_check': True,
                'enable_subpixel_analysis': True
            },
            AnalysisMode.RESEARCH: {
                'confidence_threshold': 0.15,
                'min_area_threshold': 10,
                'enable_morphology': True,
                'enable_width_analysis': True,
                'enable_quality_check': True,
                'enable_subpixel_analysis': True,
                'enable_statistical_analysis': True
            }
        }
        
        self.current_mode = AnalysisMode.BALANCED
    
    def set_analysis_mode(self, mode: AnalysisMode):
        """设置分析模式"""
        self.current_mode = mode
        print(f"✅ 分析模式已设置为: {mode.value}")
    
    def analyze_image(self, image: np.ndarray, mode: Optional[AnalysisMode] = None) -> Dict:
        """
        执行完整的图像分析
        
        Args:
            image: 输入图像
            mode: 分析模式，如果为None则使用当前模式
            
        Returns:
            完整的分析结果
        """
        if mode is None:
            mode = self.current_mode
        
        config = self.analysis_configs[mode]
        start_time = time.time()
        
        print(f"🔍 开始{mode.value}模式分析...")
        
        # 第一步：图像预处理和特征分析
        image_analysis = self._analyze_image_properties(image)
        
        # 第二步：智能模型选择
        recommended_model = self.segmentation_system.recommend_model(image)
        if self.segmentation_system.current_model_name != recommended_model:
            print(f"🔄 切换到推荐模型: {recommended_model}")
            self.segmentation_system.select_model(recommended_model)
        
        # 第三步：执行分割
        mask, segmentation_info = self.segmentation_system.segment_cracks(
            image, config['confidence_threshold']
        )
        
        if mask is None:
            error_msg = segmentation_info.get('error', '分割失败')

            # 提供详细的错误信息和建议
            suggestions = [
                "1. 降低置信度阈值（当前: {:.2f}）".format(config['confidence_threshold']),
                "2. 检查图像质量和对比度",
                "3. 确保图像中包含明显的裂缝",
                "4. 尝试使用传统检测方法",
                "5. 调整图像预处理参数"
            ]

            return {
                'success': False,
                'error': error_msg,
                'analysis_time': time.time() - start_time,
                'suggestions': suggestions,
                'image_properties': self._analyze_image_properties(image),
                'recommended_actions': [
                    "切换到传统检测方法",
                    "调整检测参数",
                    "检查图像预处理"
                ]
            }
        
        # 第四步：面积计算
        area_results = self.segmentation_system.calculate_precise_area(mask)
        
        # 第五步：形态学分析（如果启用）
        morphology_results = {}
        if config.get('enable_morphology', False):
            morphology_results = self._analyze_morphology(mask, image)
        
        # 第六步：宽度分析（如果启用）
        width_results = {}
        if config.get('enable_width_analysis', False):
            width_results = self._analyze_crack_width(mask)
        
        # 第七步：质量评估（如果启用）
        quality_results = {}
        if config.get('enable_quality_check', False):
            quality_results = self._assess_result_quality(mask, image, area_results)
        
        # 第八步：统计分析（研究模式）
        statistical_results = {}
        if config.get('enable_statistical_analysis', False):
            statistical_results = self._perform_statistical_analysis(mask, area_results)
        
        # 第九步：生成综合结果
        analysis_time = time.time() - start_time
        
        comprehensive_results = {
            'success': True,
            'analysis_mode': mode.value,
            'analysis_time': analysis_time,
            'image_properties': image_analysis,
            'segmentation_info': segmentation_info,
            'area_analysis': area_results,
            'morphology_analysis': morphology_results,
            'width_analysis': width_results,
            'quality_assessment': quality_results,
            'statistical_analysis': statistical_results,
            'recommendations': self._generate_recommendations(area_results, quality_results),
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ 分析完成，耗时: {analysis_time:.2f}秒")
        
        return comprehensive_results
    
    def _analyze_image_properties(self, image: np.ndarray) -> Dict:
        """分析图像属性"""
        height, width = image.shape[:2]
        
        # 基本属性
        properties = {
            'dimensions': {'width': width, 'height': height},
            'total_pixels': width * height,
            'aspect_ratio': width / height
        }
        
        # 颜色空间分析
        if len(image.shape) == 3:
            properties['color_channels'] = image.shape[2]
            properties['is_color'] = True
            
            # 转换为灰度进行分析
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            properties['color_channels'] = 1
            properties['is_color'] = False
            gray = image
        
        # 亮度和对比度分析
        properties['brightness'] = {
            'mean': float(np.mean(gray)),
            'std': float(np.std(gray)),
            'min': int(np.min(gray)),
            'max': int(np.max(gray))
        }
        
        # 纹理分析
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        properties['texture_variance'] = float(laplacian.var())
        
        # 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        properties['edge_density'] = float(np.sum(edges > 0) / (width * height))
        
        return properties
    
    def _analyze_morphology(self, mask: np.ndarray, image: np.ndarray) -> Dict:
        """形态学分析"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {'error': '没有找到有效轮廓'}
        
        morphology_features = []
        
        for i, contour in enumerate(contours):
            if cv2.contourArea(contour) < 10:
                continue
            
            # 基本几何特征
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            
            # 外接矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # 最小外接矩形
            rect = cv2.minAreaRect(contour)
            box = cv2.boxPoints(rect)
            box = np.int32(box)
            
            # 椭圆拟合
            if len(contour) >= 5:
                ellipse = cv2.fitEllipse(contour)
                ellipse_area = np.pi * ellipse[1][0] * ellipse[1][1] / 4
            else:
                ellipse_area = area
            
            # 凸包
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            
            # 计算形状特征
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
            solidity = area / hull_area if hull_area > 0 else 0
            extent = area / (w * h) if w * h > 0 else 0
            
            # 方向角
            if len(contour) >= 5:
                orientation = rect[2]
            else:
                orientation = 0
            
            # 粗糙度（周长与凸包周长的比值）
            hull_perimeter = cv2.arcLength(hull, True)
            roughness = perimeter / hull_perimeter if hull_perimeter > 0 else 1
            
            features = CrackFeatures(
                area=area * (self.scale_factor ** 2),
                length=max(w, h) * self.scale_factor,
                width=min(w, h) * self.scale_factor,
                aspect_ratio=aspect_ratio,
                circularity=circularity,
                solidity=solidity,
                orientation=orientation,
                roughness=roughness
            )
            
            morphology_features.append({
                'contour_id': i,
                'features': features.__dict__,
                'bounding_rect': {'x': x, 'y': y, 'width': w, 'height': h},
                'extent': extent
            })
        
        # 计算整体统计
        if morphology_features:
            all_features = [f['features'] for f in morphology_features]
            
            statistics = {}
            for key in all_features[0].keys():
                values = [f[key] for f in all_features]
                statistics[key] = {
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values)),
                    'min': float(np.min(values)),
                    'max': float(np.max(values)),
                    'median': float(np.median(values))
                }
        else:
            statistics = {}
        
        return {
            'individual_features': morphology_features,
            'statistics': statistics,
            'total_contours': len(morphology_features)
        }
    
    def _analyze_crack_width(self, mask: np.ndarray) -> Dict:
        """分析裂缝宽度"""
        try:
            # 尝试使用OpenCV的骨架化
            skeleton = cv2.ximgproc.thinning(mask)
        except AttributeError:
            # 如果ximgproc不可用，使用形态学操作近似
            kernel = cv2.getStructuringElement(cv2.MORPH_CROSS, (3, 3))
            skeleton = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 距离变换
        dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        
        # 在骨架点上采样宽度
        skeleton_points = np.where(skeleton > 0)
        
        if len(skeleton_points[0]) == 0:
            return {'error': '无法提取裂缝骨架'}
        
        widths_pixel = []
        for y, x in zip(skeleton_points[0], skeleton_points[1]):
            width_pixel = dist_transform[y, x] * 2  # 距离变换给出的是到边界的距离
            widths_pixel.append(width_pixel)
        
        # 转换为实际单位
        widths_real = [w * self.scale_factor for w in widths_pixel]
        
        if not widths_real:
            return {'error': '没有有效的宽度测量'}
        
        return {
            'width_measurements': {
                'pixel_widths': widths_pixel,
                'real_widths': widths_real,
                'measurement_points': len(widths_real)
            },
            'width_statistics': {
                'mean_width': np.mean(widths_real),
                'std_width': np.std(widths_real),
                'min_width': np.min(widths_real),
                'max_width': np.max(widths_real),
                'median_width': np.median(widths_real)
            },
            'unit': self.unit
        }
    
    def _assess_result_quality(self, mask: np.ndarray, image: np.ndarray, area_results: Dict) -> Dict:
        """评估结果质量"""
        quality_score = 0
        issues = []
        recommendations = []
        
        # 检查1：分割完整性
        if area_results['contour_count'] == 0:
            issues.append('没有检测到裂缝')
            quality_score = 0
        else:
            quality_score += 0.3
        
        # 检查2：面积合理性
        total_area_ratio = area_results['total_pixel_area'] / (image.shape[0] * image.shape[1])
        if total_area_ratio < 0.001:
            issues.append('检测到的裂缝面积过小')
            recommendations.append('降低检测阈值或检查图像质量')
        elif total_area_ratio > 0.1:
            issues.append('检测到的裂缝面积过大，可能存在误检')
            recommendations.append('提高检测阈值或检查图像预处理')
        else:
            quality_score += 0.3
        
        # 检查3：形状合理性
        if area_results['contour_count'] > 0:
            avg_area = area_results['area_statistics']['mean_area']
            std_area = area_results['area_statistics']['std_area']
            
            if std_area / avg_area > 2:  # 变异系数过大
                issues.append('裂缝面积分布不均匀')
                recommendations.append('检查是否存在噪声或多种类型的缺陷')
            else:
                quality_score += 0.2
        
        # 检查4：连通性
        num_labels, labels = cv2.connectedComponents(mask)
        if num_labels > area_results['contour_count'] * 1.5:
            issues.append('分割结果过于碎片化')
            recommendations.append('调整形态学操作参数或后处理')
        else:
            quality_score += 0.2
        
        # 质量等级
        if quality_score >= 0.8:
            quality_level = 'excellent'
        elif quality_score >= 0.6:
            quality_level = 'good'
        elif quality_score >= 0.4:
            quality_level = 'fair'
        else:
            quality_level = 'poor'
        
        return {
            'quality_score': quality_score,
            'quality_level': quality_level,
            'issues': issues,
            'recommendations': recommendations,
            'metrics': {
                'area_ratio': total_area_ratio,
                'num_components': num_labels - 1,
                'fragmentation_ratio': (num_labels - 1) / max(1, area_results['contour_count'])
            }
        }
    
    def _perform_statistical_analysis(self, mask: np.ndarray, area_results: Dict) -> Dict:
        """执行统计分析"""
        if area_results['contour_count'] == 0:
            return {'error': '没有数据进行统计分析'}
        
        areas = area_results['individual_real_areas']
        
        # 描述性统计
        descriptive_stats = {
            'count': len(areas),
            'mean': np.mean(areas),
            'std': np.std(areas),
            'min': np.min(areas),
            'max': np.max(areas),
            'median': np.median(areas),
            'q25': np.percentile(areas, 25),
            'q75': np.percentile(areas, 75),
            'skewness': self._calculate_skewness(areas),
            'kurtosis': self._calculate_kurtosis(areas)
        }
        
        # 分布分析
        distribution_analysis = {
            'coefficient_of_variation': descriptive_stats['std'] / descriptive_stats['mean'] if descriptive_stats['mean'] > 0 else 0,
            'range': descriptive_stats['max'] - descriptive_stats['min'],
            'iqr': descriptive_stats['q75'] - descriptive_stats['q25']
        }
        
        return {
            'descriptive_statistics': descriptive_stats,
            'distribution_analysis': distribution_analysis
        }
    
    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0
        
        mean = np.mean(data)
        std = np.std(data)
        
        if std == 0:
            return 0
        
        skewness = np.mean([(x - mean) ** 3 for x in data]) / (std ** 3)
        return skewness
    
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0
        
        mean = np.mean(data)
        std = np.std(data)
        
        if std == 0:
            return 0
        
        kurtosis = np.mean([(x - mean) ** 4 for x in data]) / (std ** 4) - 3
        return kurtosis
    
    def _generate_recommendations(self, area_results: Dict, quality_results: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if area_results['contour_count'] == 0:
            recommendations.extend([
                '尝试降低检测阈值',
                '检查图像质量和对比度',
                '考虑使用图像增强技术',
                '验证比例尺设置是否正确'
            ])
        
        if quality_results and quality_results.get('quality_score', 0) < 0.6:
            recommendations.extend(quality_results.get('recommendations', []))
        
        if area_results['contour_count'] > 20:
            recommendations.append('结果可能包含噪声，建议增加最小面积阈值')
        
        return recommendations
    
    def generate_comprehensive_report(self, results: Dict, save_path: Optional[str] = None) -> str:
        """生成综合分析报告"""
        report_lines = []
        
        # 报告头部
        report_lines.extend([
            "=" * 60,
            "裂缝检测分析报告",
            "=" * 60,
            f"分析时间: {results.get('timestamp', 'Unknown')}",
            f"分析模式: {results.get('analysis_mode', 'Unknown')}",
            f"处理耗时: {results.get('analysis_time', 0):.2f}秒",
            ""
        ])
        
        # 图像信息
        if 'image_properties' in results:
            props = results['image_properties']
            report_lines.extend([
                "图像信息:",
                f"  尺寸: {props['dimensions']['width']} x {props['dimensions']['height']}",
                f"  总像素: {props['total_pixels']:,}",
                f"  长宽比: {props['aspect_ratio']:.2f}",
                f"  平均亮度: {props['brightness']['mean']:.1f}",
                f"  对比度: {props['brightness']['std']:.1f}",
                ""
            ])
        
        # 检测结果
        if 'area_analysis' in results:
            area = results['area_analysis']
            report_lines.extend([
                "检测结果:",
                f"  裂缝数量: {area['contour_count']}",
                f"  总面积: {area['total_real_area']:.2f} {area['unit']}",
                f"  平均面积: {area['area_statistics']['mean_area']:.2f} {area['unit']}",
                f"  最大面积: {area['area_statistics']['max_area']:.2f} {area['unit']}",
                f"  最小面积: {area['area_statistics']['min_area']:.2f} {area['unit']}",
                ""
            ])
        
        # 质量评估
        if 'quality_assessment' in results and results['quality_assessment']:
            quality = results['quality_assessment']
            report_lines.extend([
                "质量评估:",
                f"  质量评分: {quality['quality_score']:.2f}",
                f"  质量等级: {quality['quality_level']}",
                ""
            ])
            
            if quality.get('issues'):
                report_lines.append("发现的问题:")
                for issue in quality['issues']:
                    report_lines.append(f"  - {issue}")
                report_lines.append("")
        
        # 改进建议
        if 'recommendations' in results and results['recommendations']:
            report_lines.append("改进建议:")
            for rec in results['recommendations']:
                report_lines.append(f"  - {rec}")
            report_lines.append("")
        
        report_text = "\n".join(report_lines)
        
        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"✅ 报告已保存到: {save_path}")
        
        return report_text
