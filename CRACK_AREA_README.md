# 裂缝面积识别系统


- 计算裂缝的像素面积
- 支持单张图片和批量文件夹处理
- 生成裂缝面积对比图表
##系统要求
- Python 3.8+
- Ultralytics- PyTorch
- Matplotlib


pip install ultralytics opencv-python matplotlib numpy



```bash
```
2. 文件夹批量检测:
python crack_area_detection.py --source path/to/folder --save --show

3. bash添加比例尺(例如1像素=0.1mm²):
```
###批处理文件使用
1. 直接将图片拖放到`run_crack_detection.bat`文件上
```bash
run_crack_detection.bat path/to/folder
```
## 参数说明
- `--model`: YOLOv11分割模型路径，默认为"yolo11n-seg.pt"
- `--conf`: 置信度阈值，默认为0.25
- `--show`: 显示检测结果
## 输出结果

2. CSV格式的面积数据报告


- 为获得更准确的结果，建议使用高分辨率图像


处理图片 1/3: crack_sample.jpg
  - 像素面积: 3245 像素
  - 置信度: 0.92
实际面积: 324.50 mm²
```