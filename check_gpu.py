# -*- coding: utf-8 -*-
"""
快速GPU环境检查脚本
"""

import subprocess
import sys

def check_nvidia_gpu():
    """检查NVIDIA GPU"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA GPU检测成功")
            print(result.stdout)
            return True
        else:
            print("❌ nvidia-smi命令失败")
            return False
    except:
        print("❌ 无法运行nvidia-smi")
        return False

def check_pytorch():
    """检查PyTorch CUDA支持"""
    try:
        import torch
        print(f"\n✅ PyTorch版本: {torch.__version__}")
        
        cuda_available = torch.cuda.is_available()
        print(f"CUDA可用: {cuda_available}")
        
        if cuda_available:
            device_count = torch.cuda.device_count()
            print(f"GPU数量: {device_count}")
            
            for i in range(device_count):
                device_name = torch.cuda.get_device_name(i)
                memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {device_name} ({memory:.1f} GB)")
            
            # 测试GPU计算
            print("\n🧪 测试GPU计算...")
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ GPU计算测试成功")
            
            return True
        else:
            print("❌ CUDA不可用")
            print("\n🔧 解决方案:")
            print("1. 安装GPU版本的PyTorch:")
            print("   pip uninstall torch torchvision torchaudio")
            print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
            print("2. 重启Python环境")
            return False
            
    except ImportError:
        print("❌ PyTorch未安装")
        print("\n🔧 安装PyTorch GPU版本:")
        print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
        return False
    except Exception as e:
        print(f"❌ PyTorch检查失败: {e}")
        return False

def check_ultralytics():
    """检查ultralytics"""
    try:
        from ultralytics import YOLO, __version__
        print(f"\n✅ Ultralytics版本: {__version__}")
        
        # 测试模型加载
        print("🧪 测试YOLO模型加载...")
        model = YOLO('yolo11n.pt')  # 会自动下载
        print("✅ YOLO模型加载成功")
        
        return True
    except ImportError:
        print("❌ Ultralytics未安装")
        print("\n🔧 安装Ultralytics:")
        print("pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ Ultralytics检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=== GPU环境快速检查 ===\n")
    
    # 检查NVIDIA GPU
    gpu_ok = check_nvidia_gpu()
    
    # 检查PyTorch
    pytorch_ok = check_pytorch()
    
    # 检查Ultralytics
    ultralytics_ok = check_ultralytics()
    
    print("\n=== 检查结果 ===")
    print(f"NVIDIA GPU: {'✅' if gpu_ok else '❌'}")
    print(f"PyTorch CUDA: {'✅' if pytorch_ok else '❌'}")
    print(f"Ultralytics: {'✅' if ultralytics_ok else '❌'}")
    
    if gpu_ok and pytorch_ok and ultralytics_ok:
        print("\n🎉 GPU环境配置完成！可以开始GPU训练")
        print("运行: python train.py")
    else:
        print("\n⚠️  GPU环境需要配置")
        if not pytorch_ok:
            print("\n🔧 快速修复PyTorch CUDA:")
            print("pip uninstall torch torchvision torchaudio -y")
            print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")

if __name__ == '__main__':
    main()
