# 🖥️ 可调整大小UI界面完整指南

## ✅ **功能实现总览**

### **新增可调整UI功能**
- **🖱️ 拖拽调整**: 可拖拽分割线调整左右面板大小
- **🎨 布局预设**: 三种预设布局（紧凑/平衡/宽屏）
- **💾 状态保存**: 自动保存和恢复布局状态
- **⌨️ 快捷键**: 丰富的键盘快捷键支持
- **📱 面板折叠**: 左侧面板可折叠隐藏
- **🔳 全屏模式**: 一键切换全屏显示
- **🔄 布局重置**: 快速重置为默认布局

---

## 🖥️ **界面布局详解**

### **分割器配置**
```
主界面布局:
├── 左侧控制面板 (可调整宽度: 300-800px)
│   ├── 图像加载
│   ├── 参数设置
│   ├── 模型选择
│   ├── 宽度分析
│   ├── 操作按钮
│   └── 界面布局控制 ⭐ 新增
└── 右侧显示区域 (最小宽度: 500px)
    ├── 原图显示
    ├── 结果显示
    ├── 宽度分析
    └── 文本结果
```

### **新增界面布局控制组**
```
🖥️ 界面布局
├── [紧凑] [平衡] [宽屏]     # 布局预设按钮
├── [🔄 重置] [💾 保存]      # 控制按钮
├── 当前布局: 默认            # 状态显示
└── [◀ 折叠左侧] [🔳 全屏]   # 特殊功能
```

---

## 🎨 **布局预设详解**

### **1. 紧凑布局 (Ctrl+1)**
- **比例**: 左侧25% : 右侧75%
- **特点**: 最大化显示区域，适合查看大图
- **适用**: 图像分析、结果查看

### **2. 平衡布局 (Ctrl+2)**
- **比例**: 左侧35% : 右侧65%
- **特点**: 控制和显示区域平衡
- **适用**: 日常使用、参数调整

### **3. 宽屏布局 (Ctrl+3)**
- **比例**: 左侧45% : 右侧55%
- **特点**: 更多控制空间，适合复杂操作
- **适用**: 模型选择、参数微调

---

## ⌨️ **键盘快捷键大全**

### **布局控制快捷键**
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| **Ctrl+1** | 紧凑布局 | 切换到紧凑模式 |
| **Ctrl+2** | 平衡布局 | 切换到平衡模式 |
| **Ctrl+3** | 宽屏布局 | 切换到宽屏模式 |
| **Ctrl+H** | 折叠面板 | 折叠/展开左侧面板 |
| **F11** | 全屏模式 | 进入/退出全屏 |
| **Ctrl+R** | 重置布局 | 恢复默认布局 |
| **Ctrl+S** | 保存布局 | 保存当前布局状态 |

### **功能操作快捷键**
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| **Ctrl+O** | 打开图像 | 浏览选择图像文件 |
| **Ctrl+D** | 开始检测 | 执行裂缝检测 |

---

## 🔧 **使用方法详解**

### **方法1: 鼠标拖拽调整**

#### **拖拽分割线**
1. 将鼠标悬停在左右面板之间的分割线上
2. 鼠标指针变为调整大小图标
3. 按住左键拖拽调整面板大小
4. 释放鼠标完成调整

#### **分割线特性**
- **宽度**: 6px，便于操作
- **视觉反馈**: 悬停和按下时颜色变化
- **实时调整**: 拖拽过程中实时显示效果
- **约束限制**: 自动限制最小/最大宽度

### **方法2: 布局预设按钮**

#### **快速切换布局**
1. 在"界面布局"组中点击预设按钮
2. 系统自动调整到预设比例
3. 状态栏显示当前布局信息
4. 布局状态自动保存

#### **预设按钮功能**
- **紧凑**: 最大化右侧显示区域
- **平衡**: 左右面板均衡分配
- **宽屏**: 增加左侧控制空间

### **方法3: 键盘快捷键**

#### **快速布局切换**
- **Ctrl+1**: 一键切换到紧凑布局
- **Ctrl+2**: 一键切换到平衡布局  
- **Ctrl+3**: 一键切换到宽屏布局

#### **特殊功能快捷键**
- **Ctrl+H**: 快速折叠/展开左侧面板
- **F11**: 进入/退出全屏模式
- **Ctrl+R**: 重置为默认布局

### **方法4: 面板折叠**

#### **折叠左侧面板**
1. 点击"◀ 折叠左侧"按钮
2. 左侧面板完全隐藏
3. 右侧显示区域占满整个窗口
4. 按钮文字变为"▶ 展开左侧"

#### **展开左侧面板**
1. 点击"▶ 展开左侧"按钮
2. 左侧面板恢复到之前的大小
3. 恢复正常的双面板布局

---

## 💾 **状态保存和恢复**

### **自动保存机制**
- **触发时机**: 每次调整分割器大小时自动保存
- **保存内容**: 面板大小比例、窗口位置和尺寸
- **保存位置**: `config/splitter_layout.json`

### **配置文件格式**
```json
{
  "sizes": [420, 980],
  "geometry": [100, 100, 1400, 900],
  "saved_time": "2024-01-01T12:00:00"
}
```

### **自动恢复**
- **启动时**: 自动加载保存的布局配置
- **失败处理**: 如果配置无效，使用默认布局
- **兼容性**: 支持不同屏幕分辨率的适配

---

## 📊 **测试验证结果**

### **功能测试结果**
```
✅ 分割器配置: 通过
✅ 布局预设: 通过
✅ 布局持久化: 通过
✅ 键盘快捷键: 通过
✅ 面板折叠: 通过

分割器属性:
- 方向: 水平
- 手柄宽度: 6px
- 子部件数量: 2
- 允许折叠: True
- 实时调整: True
```

### **布局测试结果**
```
紧凑布局: 25% : 75% ✅
平衡布局: 35% : 65% ✅  
宽屏布局: 45% : 55% ✅
面板折叠: 0px : 100% ✅
面板展开: 恢复原比例 ✅
```

### **快捷键测试**
```
✅ Ctrl+1/2/3: 布局切换
✅ Ctrl+H: 面板折叠
✅ F11: 全屏模式
✅ Ctrl+R: 布局重置
✅ Ctrl+O/D/S: 功能快捷键
```

---

## 🎯 **使用场景和建议**

### **不同场景的最佳布局**

#### **🔍 图像分析场景**
- **推荐布局**: 紧凑布局 (Ctrl+1)
- **优势**: 最大化图像显示区域
- **操作**: 快速查看检测结果和细节

#### **⚙️ 参数调整场景**
- **推荐布局**: 平衡布局 (Ctrl+2)
- **优势**: 控制和显示区域均衡
- **操作**: 方便调整参数并观察效果

#### **🤖 模型选择场景**
- **推荐布局**: 宽屏布局 (Ctrl+3)
- **优势**: 更多控制面板空间
- **操作**: 便于模型对比和配置

#### **📊 结果展示场景**
- **推荐操作**: 折叠左侧面板 (Ctrl+H)
- **优势**: 全屏显示检测结果
- **操作**: 专注于结果分析

#### **🎓 演示教学场景**
- **推荐操作**: 全屏模式 (F11)
- **优势**: 最大化显示效果
- **操作**: 适合投影和演示

---

## 💡 **最佳实践建议**

### **日常使用建议**
1. **默认布局**: 使用平衡布局作为日常工作布局
2. **快捷切换**: 熟练使用Ctrl+1/2/3快速切换
3. **自动保存**: 调整到满意的布局后会自动保存
4. **重置功能**: 遇到布局问题时使用Ctrl+R重置

### **效率提升技巧**
1. **键盘优先**: 使用快捷键比鼠标操作更快
2. **场景切换**: 根据不同任务快速切换布局
3. **面板折叠**: 需要专注时折叠控制面板
4. **全屏模式**: 演示或详细分析时使用全屏

### **个性化定制**
1. **调整到最适合的比例**: 根据屏幕大小和使用习惯
2. **保存个人配置**: 系统会记住你的偏好设置
3. **多场景配置**: 可以为不同任务保存不同布局

---

## 🚀 **快速开始**

### **启动系统**
```bash
python enhanced_crack_gui.py
```

### **快速体验流程**
1. **拖拽体验**: 拖拽中间分割线感受调整效果
2. **预设切换**: 点击紧凑/平衡/宽屏按钮体验不同布局
3. **快捷键**: 尝试Ctrl+1/2/3快速切换
4. **面板折叠**: 使用Ctrl+H体验面板折叠功能
5. **全屏模式**: 按F11体验全屏效果

### **个性化设置**
1. **调整到喜欢的比例**: 拖拽到最舒适的大小
2. **测试不同场景**: 在不同任务中找到最佳布局
3. **保存配置**: 系统会自动保存你的设置

---

## 🔧 **技术实现特点**

### **分割器增强**
- **样式美化**: 自定义分割线样式和交互效果
- **约束控制**: 智能的最小/最大宽度限制
- **实时反馈**: 拖拽过程中的实时状态显示

### **状态管理**
- **自动保存**: 每次调整后自动保存状态
- **智能恢复**: 启动时智能恢复保存的布局
- **错误处理**: 配置文件损坏时的优雅降级

### **用户体验**
- **视觉反馈**: 清晰的状态显示和操作提示
- **操作便捷**: 多种操作方式满足不同用户习惯
- **响应迅速**: 流畅的调整和切换体验

---

**🎉 现在系统具备了专业级的可调整UI界面，用户可以根据需要自由调整布局，获得最佳的使用体验！**
